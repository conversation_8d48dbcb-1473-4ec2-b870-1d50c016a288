# A/B Testing Components

This directory contains reusable components for implementing A/B testing across onboarding pages.

## Components

- `ComponentVariantRenderer.tsx` - Core component that selects which variant to render
- `withComponentVariants.tsx` - Higher-Order Component for easy A/B testing integration

## Usage

### Basic Usage

```typescript
import { withComponentVariants } from '@/components/onboarding/withComponentVariants';

// Your default component implementation
const MyPageDefault = () => {
  return <div>Default version of the page</div>;
};

// Your A/B test variant
const MyPageVariantA = () => {
  return <div>Variant A with different copy</div>;
};

// Export using the HOC
export default withComponentVariants(
  MyPageDefault,
  {
    'my-page-variant-a': MyPageVariantA,
    'my-page-variant-b': MyPageVariantB, // You can have multiple variants
  }
);
```

### How It Works

1. The backend sends `component: "my-page-variant-a"` in the onboarding data
2. `withComponentVariants` HOC uses `useOnboardingNavigation` to get the override
3. `ComponentVariantRenderer` selects the appropriate component to render
4. If no override or unknown variant → renders the default component

### Example: Info Page A/B Test

```typescript
// apps/patients/src/app/(connected)/onboarding/(onboarding)/info/page.tsx
import { withComponentVariants } from '@/components/onboarding/withComponentVariants';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
// ... other imports

// Original component extracted out
const InfoDefault = () => {
  const { onboarding, nextOnboardingStep } = useOnboardingNavigation();

  return (
    <OnboardingTitle
      title="Default Title"
      subtitle="Default subtitle text"
    >
      {/* existing implementation */}
    </OnboardingTitle>
  );
};

// A/B test variant extracted out
const InfoVariantA = () => {
  const { onboarding, nextOnboardingStep } = useOnboardingNavigation();

  return (
    <OnboardingTitle
      title="A/B Test Title"
      subtitle="Alternative subtitle for testing"
    >
      {/* same implementation, different copy */}
    </OnboardingTitle>
  );
};

// Export using the HOC, mapping variant keys to components
export default withComponentVariants(
  InfoDefault,
  {
    'info-variant-a': InfoVariantA,
  }
);
```

### Backend Integration

In the backend, for the states to be versioned, the state machine should have a custom `component` key and value in the state machine `meta` section, for example:

```typescript
selectTreatmentType: {
    on: {
      next: { target: 'selectTreatment', actions: 'store' },
      back: { target: 'questionnaire.additionalInformation' },
    },
    meta: {
      step: 1,
        name: 'Virtual Doctor Visit',
        component: 'info-variant-a' // <-- This key determines which variant to render
        content: {
        title: 'Select your treatment type',
          subtitle: 'Choose the type of treatment that best fits your needs',
          buttonText: 'CONTINUE',
      },
    },
}
```
