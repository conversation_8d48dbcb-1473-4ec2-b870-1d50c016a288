# Willow Project Overview

Willow is a healthcare web app for GLP-1 medications (like Semaglutide) with patient onboarding, doctor verification,
and prescription management. The platform follows domain-driven design with XState for state management.

## Architecture

- **Monorepo**: Turborepo with 3 Next.js frontends + Nest.js backend
- **Database**: Postgres with Prisma
- **UI**: Tailwind, Shadcn, Lucide icons
- **State**: React Query v5, XState v5, Joitai
- **Integrations**: Stripe (payments), DoseSpot (pharmacy), Segment (analytics)
- **Infrastructure**: Docker Compose (local), AWS (ECS Fargate for backend, Amplify for frontends)

## Key Commands

```bash
# Setup
pnpm add -F {app} {dependency}          # Add dependencies
pnpm i                                  # Install dependencies

# Output from the API, _prepend_ with `command`!!
`command tmux capture-pane -t willow:api -p -S -{lines}`

# Database
pnpm -F db migrate --name {migration}   # DONT RUN MIGRATIONS! pause and output the cli line to run the migration myself
pnpm -F restore-database start --name=clean_slate  # Initialize database from clean slate

pnpm -F backup-database start --env local --name temp-name # local snapshot backup
pnpm -F restore-database start --name temp-name # local snapshot restore

# Code Quality, run this globally
pnpm format:fix                         # Fix formatting
```

## Code Conventions

- TypeScript with strict typing
- Import types with `import type { Type } from 'module'`
- Use interfaces for type definitions
- No unused variables (prefix with _ if needed)
- No direct process.env access (use environment modules)
- No non-null assertions (!)
- Explicit error handling with try/catch
- PascalCase for components, camelCase for utilities
- Organized imports (external first, then internal)

## Testing

#### credentials

Make a POST request to the following endpoints with the following bodies, and store the accessToken in the JSON
response:

```
https://localhost:API_PORT/admin/signIn
{ email: "<EMAIL>", password: "somePassword1234$" }

https://localhost:API_PORT/doctor/signIn
{ email: "<EMAIL>", password: "somePassword1234$" }

https://localhost:API_PORT/patient/signIn
{ email: "<EMAIL>", password: "somePassword1234$" }
```

## Github

- Org: Willow-Health-Services
- Repo: willow-monorepo

## Datadog

- site: us5.datadoghq.com

## ClickUp

- team id: **********
- generate branch names like: `{custom-id}-{ticket-title-in-kebab-case}`
- add testing instructions to the bottom of the description, like:

```
### Testing

1. step 1
2. step 2
...
```

## Commit Format

Short, single line, descriptive commits with scope:

- feat(api): add endpoint to cancel patient
- fix(patients): repair cancel button functionality
- chore(doctors): resolve lint issues
- refactor(tools): improve utility functions

only possible scopes are: api, patients, doctors, admin, db, tools, lib
only possible types are: feat, fix, chore, refactor

Every msg should be: $type($scope): $msg
No quotes, no -, no **, nothing, just that structure

NEVER commit unless I ask for it

## PR Template

Title: `{ticket-id} - {meaningful-title}`, for example: `PROD-1104 - Cancel patient button`

```markdown
# Context 📝

https://app.clickup.com/t/**********/{ticket-id}

# What Changed 🔧

- {Short bullet list of changes}

# Verification ✅

1. {Steps to verify changes}
```

Don't add any indication of Claude in the PR

Ensure CI passes formatting, linting, and tests before requesting review.
