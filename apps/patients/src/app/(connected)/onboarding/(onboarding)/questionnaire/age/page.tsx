'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { DateInput } from '@/components/ui/date-input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { getStartedAtom, onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import dayjs from 'dayjs';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  birthDate: z
    .string()
    .refine((date) => dayjs(date, 'MM/DD/YYYY').isValid(), {
      message: 'Invalid date format',
    })
    .refine(
      (date) => {
        const birthDate = dayjs(date, 'MM/DD/YYYY');
        const overAge = dayjs().diff(birthDate, 'year') > 200;
        return !overAge;
      },
      {
        message: 'Invalid date',
      },
    ),
});

type FormType = z.infer<typeof schema>;

const AgeQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();

  const setSteps = useSetAtom(onboardingStepsAtom);
  const setGetStarted = useSetAtom(getStartedAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const birthDateValue =
    getOnboardingFieldValue<string>(onboarding, 'birthDate', {
      aliases: ['age'],
    }) ?? '';

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      birthDate: birthDateValue,
    },
    shouldFocusError: true,
  });

  useEffect(() => {
    if (!onboarding) return;
    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  useEffect(() => {
    form.setValue('birthDate', birthDateValue ?? '');
  }, [birthDateValue, form]);

  if (!onboarding) return null;

  const onSubmit = async ({ birthDate }: FormType) => {
    form.clearErrors();
    setIsSubmitting(true);

    try {
      const response = await dispatch({ event: 'next', value: { birthDate } });

      const { validationError } = response;
      if (validationError?.errors?.length) {
        validationError.errors.forEach(
          (errorItem: { field: string; message: string }) => {
            if (errorItem.field === 'birthDate') {
              form.setError('birthDate', { message: errorItem.message });
            }
          },
        );
        setIsSubmitting(false);
        return;
      }

      if (validationError?.message) {
        form.setError('birthDate', { message: validationError.message });
        setIsSubmitting(false);
        return;
      }

      setGetStarted((prev) => ({
        ...prev,
        birthday: birthDate,
      }));
    } catch (error: any) {
      form.setError('birthDate', {
        message: error?.response?.data?.message || 'Something went wrong',
      });
      setIsSubmitting(false);
      return;
    }
  };

  return (
    <OnboardingTitle
      title={onboarding?.content?.title || "What's your birthday?"}
      subtitle={
        onboarding?.content?.subtitle ||
        "Age helps us determine if you're eligible for Willow's program."
      }
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col items-center gap-10"
        >
          <FormLoader isLoading={isSubmitting || isDispatching}>
            <FormField
              control={form.control}
              name="birthDate"
              render={({ field }) => (
                <FormItem className="grid w-full gap-1 py-2">
                  <FormControl>
                    <DateInput
                      autoFocus={true}
                      {...field}
                      type="text"
                      placeholder="MM/DD/YYYY"
                      className="w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={isSubmitting || isDispatching}
            >
              <span>{onboarding?.content?.buttonText || 'CONTINUE'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default AgeQuestionnaire;
