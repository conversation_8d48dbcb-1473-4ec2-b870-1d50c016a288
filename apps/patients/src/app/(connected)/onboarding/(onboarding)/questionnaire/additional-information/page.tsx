'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useTracking } from '~/analytics/use-tracking';
import { useAnalyticsData } from '~/hooks/useAnalyticsData';

const schema = z.object({
  additionalInformation: z.string().optional(),
});

type FormType = z.infer<typeof schema>;

const AdditionalInformationQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const analyticsData = useAnalyticsData();
  const { track } = useTracking();

  const setSteps = useSetAtom(onboardingStepsAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const additionalInformationValue =
    getOnboardingFieldValue<string | undefined>(
      onboarding,
      'additionalInformation',
    ) ?? '';

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      additionalInformation: additionalInformationValue,
    },
  });

  useEffect(() => {
    if (!onboarding) return;
    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  useEffect(() => {
    form.setValue('additionalInformation', additionalInformationValue ?? '');
  }, [additionalInformationValue, form]);

  if (!onboarding) return null;

  const hasNext = (onboarding.events || []).includes('next');

  const onSubmit = async ({ additionalInformation }: FormType) => {
    form.clearErrors();
    setIsSubmitting(true);

    if (!hasNext) {
      setIsSubmitting(false);
      return;
    }

    try {
      track('Questionnaire Completed', analyticsData);
      const response = await dispatch({
        event: 'next',
        value: { additionalInformation: additionalInformation ?? '' },
      });

      const validationError = (response as any)?.validationError;
      if (validationError?.errors?.length) {
        validationError.errors.forEach(
          (errorItem: { field: string; message: string }) => {
            if (errorItem.field === 'additionalInformation') {
              form.setError('additionalInformation', {
                message: errorItem.message,
              });
            }
          },
        );
        setIsSubmitting(false);
        return;
      }

      if (validationError?.message) {
        form.setError('additionalInformation', {
          message: validationError.message,
        });
        setIsSubmitting(false);
        return;
      }
    } catch (error: any) {
      form.setError('additionalInformation', {
        message: error?.response?.data?.message || 'Something went wrong',
      });
      setIsSubmitting(false);
      return;
    }
  };

  const value = form.watch('additionalInformation') ?? '';

  return (
    <OnboardingTitle
      title={
        onboarding?.content?.title ||
        'Is there anything else you would like the doctor to know?'
      }
      subtitle={
        onboarding?.content?.subtitle ||
        'Please describe anything else you would like to communicate to the Willow medical team.'
      }
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col items-center gap-10"
        >
          <FormLoader isLoading={isSubmitting || isDispatching}>
            <FormField
              control={form.control}
              name="additionalInformation"
              render={({ field }) => (
                <FormItem className="grid w-full gap-1 py-2">
                  <FormControl>
                    <Textarea
                      autoFocus={true}
                      {...field}
                      placeholder="Write something here"
                      className="text-md relative h-60 w-full rounded-lg border-none bg-glass p-5 font-light text-white placeholder:text-stone-300"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={!hasNext || isSubmitting || isDispatching}
            >
              <span>{value.trim() ? 'CONTINUE' : 'NOTHING TO ADD'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default AdditionalInformationQuestionnaire;
