'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingDataAtom } from '@/store/store';
import { useAtomValue } from 'jotai';

const RejectedUnderAge = () => {
  const onboarding = useAtomValue(onboardingDataAtom);
  const { dispatch, isDispatching } = useOnboardingStateMachine();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleBack = async () => {
    setIsSubmitting(true);
    try {
      await dispatch({ event: 'back' });
    } catch (error) {
      console.error('Failed to navigate back from rejection state', error);
      setIsSubmitting(false);
    }
  };

  return (
    <OnboardingTitle
      title={onboarding?.content?.title || 'Come back in a few years!'}
      subtitle={
        onboarding?.content?.subtitle ||
        'Sorry, Willow is only available to people over the age of 18.'
      }
    >
      <Link href="https://www.startwillow.com/">
        <Button
          size="lg"
          variant="electric"
          className="flex w-full max-w-none justify-between"
          onClick={handleBack}
          disabled={isDispatching || isSubmitting}
        >
          <span>RETURN TO HOMEPAGE</span>
          <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
        </Button>
      </Link>
    </OnboardingTitle>
  );
};

export default RejectedUnderAge;
