'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import InputList from '@/components/onboarding/InputList';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { FormControl } from '@willow/ui/base/form';

const schema = z.object({
  allergies: z.array(z.string()).optional(),
});

type FormType = z.infer<typeof schema>;

const SelectAllergiesQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const initialAllergies = useMemo(() => {
    const selections = getOnboardingFieldValue<string[] | undefined>(
      onboarding,
      'allergies',
    );
    if (Array.isArray(selections) && selections.length > 0) {
      return [...selections];
    }

    return [] as string[];
  }, [onboarding]);

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      allergies: initialAllergies,
    },
  });

  useEffect(() => {
    if (!onboarding) return;
    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  useEffect(() => {
    const nextValues = initialAllergies.length > 0 ? initialAllergies : [];
    const currentValues = form.getValues('allergies') ?? [];

    const valuesMatch =
      currentValues.length === nextValues.length &&
      currentValues.every((value) => nextValues.includes(value));

    if (!valuesMatch) {
      form.setValue('allergies', nextValues);
    }
  }, [form, initialAllergies]);

  const handleUpdate = useCallback(
    (values: string[]) => {
      form.setValue('allergies', values);
    },
    [form],
  );

  if (!onboarding) return null;

  const hasNext = (onboarding.events || []).includes('next');
  const selections = form.watch('allergies') ?? [];

  const onSubmit = async ({ allergies }: FormType) => {
    setIsSubmitting(true);

    if (!hasNext) {
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await dispatch({
        event: 'next',
        value: { allergies: allergies ?? [] },
      });

      const { validationError } = response;
      if (validationError?.errors?.length) {
        validationError.errors.forEach(
          (errorItem: { field: string; message: string }) => {
            if (errorItem.field === 'allergies') {
              form.setError('allergies', { message: errorItem.message });
            }
          },
        );
        setIsSubmitting(false);
        return;
      }

      if (validationError?.message) {
        form.setError('allergies', { message: validationError.message });
        setIsSubmitting(false);
        return;
      }
    } catch (error: any) {
      form.setError('allergies', {
        message: error?.response?.data?.message || 'Something went wrong',
      });
      setIsSubmitting(false);
      return;
    }
  };

  return (
    <OnboardingTitle
      title={
        onboarding?.content?.title ||
        'What are you allergic to, and what was the allergic reaction?'
      }
      subtitle={
        onboarding?.content?.subtitle ||
        "Your medical history helps us determine if you're eligible for Willow's program."
      }
    >
      <FormLoader isLoading={isSubmitting || isDispatching}>
        <InputList
          list={initialAllergies}
          update={handleUpdate}
          name="Allergy"
        />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
            <FormField
              control={form.control}
              name="allergies"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input {...field} type="hidden" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={!hasNext || isSubmitting || isDispatching}
            >
              <span>{selections.length ? 'CONTINUE' : 'NONE'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </form>
        </Form>
      </FormLoader>
    </OnboardingTitle>
  );
};

export default SelectAllergiesQuestionnaire;
