'use client';

import { useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import InputField from '@/components/ui/InputField';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  desiredWeight: z.preprocess(
    (val) => (val === '' ? undefined : val),
    z.coerce
      .number({ message: 'Invalid Number' })
      .min(10, { message: 'Invalid Number' })
      .max(1400, { message: 'Invalid Number' }),
  ),
});

type FormValues = z.infer<typeof schema>;

const DesiredWeightQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!onboarding) return;

    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  const defaultDesiredWeight = useMemo(
    () =>
      getOnboardingFieldValue<number | undefined>(onboarding, 'desiredWeight'),
    [onboarding],
  );

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      desiredWeight: defaultDesiredWeight ?? undefined,
    },
    shouldFocusError: true,
  });

  useEffect(() => {
    if (defaultDesiredWeight === undefined) return;

    const currentValue = form.getValues('desiredWeight');

    if (currentValue === defaultDesiredWeight) {
      return;
    }

    form.setValue('desiredWeight', defaultDesiredWeight ?? 0, {
      shouldDirty: false,
      shouldTouch: false,
    });
  }, [defaultDesiredWeight, form]);

  if (!onboarding) return null;

  const hasNext = (onboarding.events || []).includes('next');

  const onSubmit = async ({ desiredWeight }: FormValues) => {
    form.clearErrors();
    setIsSubmitting(true);

    if (!hasNext) {
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await dispatch({
        event: 'next',
        value: { desiredWeight },
      });

      const { validationError } = response;
      if (validationError?.errors?.length) {
        validationError.errors.forEach(({ field, message }) => {
          if (field === 'desiredWeight') {
            form.setError('desiredWeight', { message });
          }
        });
        setIsSubmitting(false);
        return;
      }

      if (validationError?.message) {
        form.setError('desiredWeight', { message: validationError.message });
        setIsSubmitting(false);
        return;
      }
    } catch (error: unknown) {
      const maybeError = error as {
        response?: { data?: { message?: string } };
      };
      form.setError('desiredWeight', {
        message: maybeError?.response?.data?.message ?? 'Something went wrong',
      });
      setIsSubmitting(false);
      return;
    }
  };

  return (
    <OnboardingTitle
      title={
        onboarding.content?.title ?? 'What would you like your weight to be?'
      }
      subtitle={
        onboarding.content?.subtitle ??
        'Your body composition goals help us determine your ideal treatment.'
      }
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col items-center gap-10"
        >
          <FormLoader isLoading={isSubmitting || isDispatching}>
            <InputField
              control={form.control}
              name="desiredWeight"
              type="number"
              placeholder="Pounds"
              variant="dark"
              tooltip="Please enter a numerical value"
              measure="Lbs"
              autoFocus={true}
            />

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={!hasNext || isSubmitting || isDispatching}
            >
              <span>{onboarding.content?.buttonText ?? 'CONTINUE'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default DesiredWeightQuestionnaire;
