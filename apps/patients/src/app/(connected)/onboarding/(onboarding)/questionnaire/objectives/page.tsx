'use client';

import { useEffect } from 'react';
import CheckboxListQuestion from '@/components/onboarding/CheckboxListQuestion';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';

const OPTIONS = [
  {
    id: 'loseFatWithoutLosingMuscle',
    label: 'I want to lose fat without losing muscle',
  },
  {
    id: 'decreaseFatigueIncreaseEnergy',
    label: 'I want to decrease fatigue and increase my energy',
  },
  {
    id: 'supportHeartHealth',
    label: "I'm interested in supporting my heart health",
  },
  {
    id: 'improveSkinLookAndFeel',
    label: "I'd like to improve the look and feel of my skin",
  },
  { id: 'dosingConcerns', label: "I'm concerned about dosing correctly" },
  {
    id: 'noRefrigerationNeeded',
    label: "I need medication that doesn't require refrigeration",
  },
  { id: 'travelFriendly', label: "I'd like something travel-friendly" },
];

const NONE_OPTION = { id: 'none', label: 'No, none of these apply to me' };

const ObjectivesQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);

  useEffect(() => {
    if (!onboarding) return;
    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  if (!onboarding) return null;

  return (
    <CheckboxListQuestion
      onboarding={onboarding}
      isBusy={isDispatching}
      onSubmit={({ event, value }) => dispatch({ event, value })}
      config={{
        fieldName: 'objectives',
        fallbackTitle: 'Which of the following apply to you?',
        fallbackSubtitle:
          "Willow exclusively offers personalized treatments tailored to a patient's unique needs.",
        options: OPTIONS,
        noneOption: NONE_OPTION,
        errorMessage: 'Please select at least one option',
        event: 'next',
        validationField: 'objectives',
      }}
    />
  );
};

export default ObjectivesQuestionnaire;
