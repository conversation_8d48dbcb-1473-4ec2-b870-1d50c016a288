'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingDataAtom } from '@/store/store';
import { useAtomValue } from 'jotai';

const RejectedOverAge = () => {
  const onboarding = useAtomValue(onboardingDataAtom);
  const { dispatch, isDispatching } = useOnboardingStateMachine();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleBack = async () => {
    setIsSubmitting(true);
    try {
      await dispatch({ event: 'back' });
    } catch (error) {
      console.error('Failed to navigate back from rejection state', error);
      setIsSubmitting(false);
    }
  };

  return (
    <OnboardingTitle
      title={
        onboarding?.content?.title ||
        'Sorry, we do not offer treatment for those over 75'
      }
      subtitle={
        onboarding?.content?.subtitle ||
        'Sorry patients older than 75 are not eligible for treatment'
      }
    >
      <Link href="https://www.startwillow.com/">
        <Button
          size="lg"
          variant="electric"
          className="flex w-full max-w-none justify-between"
          onClick={handleBack}
          disabled={isDispatching || isSubmitting}
        >
          <span>RETURN TO HOMEPAGE</span>
          <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
        </Button>
      </Link>
    </OnboardingTitle>
  );
};

export default RejectedOverAge;
