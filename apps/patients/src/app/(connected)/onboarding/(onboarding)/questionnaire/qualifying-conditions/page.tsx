'use client';

import { useEffect } from 'react';
import CheckboxListQuestion from '@/components/onboarding/CheckboxListQuestion';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';

const QUALIFYING_OPTIONS = [
  { id: 'highBloodPressure', label: 'High Blood Pressure' },
  { id: 'highLipids', label: 'High Lipids' },
  { id: 'highCholesterol', label: 'High Cholesterol' },
  { id: 'obstructiveSleepApnea', label: 'Obstructive Sleep Apnea' },
  { id: 'cardiovascularDisease', label: 'Cardiovascular Disease' },
  { id: 'anorexiaNervosa', label: 'Anorexia Nervosa' },
  { id: 'bulimiaNervosa', label: 'Bulimia Nervosa' },
];

const NONE_OPTION = { id: 'none', label: 'None' };

const QualifyingConditionsQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);

  useEffect(() => {
    if (!onboarding) return;
    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  if (!onboarding) return null;

  return (
    <CheckboxListQuestion
      onboarding={onboarding}
      isBusy={isDispatching}
      onSubmit={({ event, value }) => dispatch({ event, value })}
      config={{
        fieldName: 'qualifyingConditions',
        fallbackTitle: 'Do you have any of the following medical conditions?',
        fallbackSubtitle:
          "If you have one of these medical conditions, please answer yes even if you're controlling it with treatment. If you have one of these conditions, you may qualify for medication with a lower BMI.",
        options: QUALIFYING_OPTIONS,
        noneOption: NONE_OPTION,
        errorMessage: 'Please select at least one option',
        event: 'next',
        validationField: 'qualifyingConditions',
      }}
    />
  );
};

export default QualifyingConditionsQuestionnaire;
