'use client';

import { useEffect } from 'react';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getStartedAtom, onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';

const GenderQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const setGetStarted = useSetAtom(getStartedAtom);

  useEffect(() => {
    if (!onboarding) return;

    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  if (!onboarding) return null;

  return (
    <RadioGroupQuestion
      onboarding={onboarding}
      fieldName="gender"
      options={[
        { value: 'male', label: 'Male' },
        { value: 'female', label: 'Female' },
      ]}
      fallbackTitle="What was the sex assigned to you at birth?"
      errorMessage="Please select your gender"
      isBusy={isDispatching}
      onSubmit={({ event }) => dispatch({ event })}
      onSuccess={({ value }) => {
        setGetStarted((prev) => ({
          ...prev,
          gender: value,
        }));
      }}
    />
  );
};

export default GenderQuestionnaire;
