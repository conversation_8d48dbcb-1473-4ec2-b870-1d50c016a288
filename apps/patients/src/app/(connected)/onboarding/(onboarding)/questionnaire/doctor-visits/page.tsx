'use client';

import { useEffect } from 'react';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getStartedAtom, onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';

const DoctorVisitsQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const setGetStarted = useSetAtom(getStartedAtom);

  useEffect(() => {
    if (!onboarding) return;

    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  if (!onboarding) return null;

  return (
    <RadioGroupQuestion
      onboarding={onboarding}
      fieldName="doctorVisits"
      options={[
        { value: 'yes', label: 'Yes' },
        { value: 'no', label: 'No' },
      ]}
      fallbackTitle={
        onboarding.content?.title ||
        'Have you seen a doctor in the last two years?'
      }
      fallbackSubtitle={
        onboarding.content?.subtitle ||
        "Your medical history helps us determine if you're eligible for Willow's program."
      }
      isBusy={isDispatching}
      onSubmit={({ event }) => dispatch({ event })}
      onSuccess={({ value }) => {
        setGetStarted((prev) => ({
          ...prev,
          doctorVisits: value,
        }));
      }}
    />
  );
};

export default DoctorVisitsQuestionnaire;
