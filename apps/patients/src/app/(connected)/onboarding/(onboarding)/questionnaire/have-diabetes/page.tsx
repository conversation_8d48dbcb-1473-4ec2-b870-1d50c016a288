'use client';

import { useEffect } from 'react';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';

const HaveDiabetesQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);

  useEffect(() => {
    if (!onboarding) return;

    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  if (!onboarding) return null;

  return (
    <RadioGroupQuestion
      onboarding={onboarding}
      fieldName="haveDiabetes"
      options={[
        { value: 'yes', label: 'Yes' },
        { value: 'no', label: 'No' },
      ]}
      fallbackTitle="Do you have diabetes?"
      fallbackSubtitle="Your medical history helps us determine if you're eligible for <PERSON>'s program."
      isBusy={isDispatching}
      onSubmit={({ event }) => dispatch({ event })}
    />
  );
};

export default HaveDiabetesQuestionnaire;
