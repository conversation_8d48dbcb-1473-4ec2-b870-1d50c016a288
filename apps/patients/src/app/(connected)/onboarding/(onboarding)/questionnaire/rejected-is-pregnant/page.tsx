'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import arrow from '@/assets/svg/arrow-circle.svg';
import AlternativeCard from '@/components/onboarding/AlternativeCard';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingDataAtom } from '@/store/store';
import { useAtomValue } from 'jotai';

const RejectedIsPregnant = () => {
  const onboarding = useAtomValue(onboardingDataAtom);
  const { dispatch, isDispatching } = useOnboardingStateMachine();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleBack = async () => {
    setIsSubmitting(true);
    try {
      await dispatch({ event: 'back' });
    } catch (error) {
      console.error('Failed to navigate back from rejection state', error);
      setIsSubmitting(false);
    }
  };

  return (
    <OnboardingTitle
      title="Thank you for visiting <PERSON>, unfortunately we cannot assist you if you are pregnant, lactating, or trying to get pregnant."
      subtitle="Though <PERSON>'s treatment may not be right for you, we would still like to see you achieve your goals in a safe manner. Check out the resources below for some possible alternative options."
    >
      <div className="flex flex-col gap-5">
        <Link
          href="https://www.healthline.com/health/pregnancy/how-to-lose-weight-safely"
          target="_blank"
        >
          <AlternativeCard
            title="How to Safely Lose Weight During Pregnancy"
            url="https://www.healthline.com/health/pregnancy/how-to-lose-weight-safely"
          />
        </Link>
        <Link
          href="https://health.osu.edu/health/womens-health/can-you-get-in-shape-while-pregnant"
          target="_blank"
        >
          <AlternativeCard
            title="Can you get in shape while pregnant?"
            url="https://health.osu.edu/health/womens-health/can-you-get-in-shape-while-pregnant"
          />
        </Link>
      </div>

      <Link href="https://www.startwillow.com/">
        <Button
          size="lg"
          variant="electric"
          className="flex w-full max-w-none justify-between"
          onClick={handleBack}
          disabled={isDispatching || isSubmitting}
        >
          <span>RETURN TO HOMEPAGE</span>
          <Image
            alt="arrow"
            src={arrow}
            style={{ objectFit: 'contain' }}
            className="pb-1"
          />
        </Button>
      </Link>
    </OnboardingTitle>
  );
};

export default RejectedIsPregnant;
