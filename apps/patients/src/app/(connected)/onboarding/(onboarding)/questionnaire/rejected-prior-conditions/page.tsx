'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import arrow from '@/assets/svg/arrow-circle.svg';
import AlternativeCard from '@/components/onboarding/AlternativeCard';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingDataAtom } from '@/store/store';
import { useAtomValue } from 'jotai';

const RejectedPriorConditionsPage = () => {
  const onboarding = useAtomValue(onboardingDataAtom);
  const { dispatch, isDispatching } = useOnboardingStateMachine();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleBack = async () => {
    setIsSubmitting(true);
    try {
      await dispatch({ event: 'back' });
    } catch (error) {
      console.error(
        'Failed to navigate back from rejected prior conditions',
        error,
      );
      setIsSubmitting(false);
    }
  };

  return (
    <OnboardingTitle
      title="Sorry, <PERSON> is unable to assist you with taking GLP-1 medication."
      subtitle="Though <PERSON>'s treatment may not be right for you, we would still like to see you achieve your goals in a safe manner. Check out the resources below for some possible alternative options."
    >
      <div className="flex flex-col gap-5">
        <Link
          href="https://www.webmd.com/diet/lose-weight-fast"
          target="_blank"
        >
          <AlternativeCard
            title="How to Lose Weight Safely"
            url="https://www.webmd.com/diet/lose-weight-fast"
          />
        </Link>
        <Link
          href="https://www.cdc.gov/healthy-weight-growth/losing-weight/"
          target="_blank"
        >
          <AlternativeCard
            title="Losing Weight"
            url="https://www.cdc.gov/healthyweight/losing_weight"
          />
        </Link>
      </div>

      <Link href="https://www.startwillow.com/">
        <Button
          size="lg"
          variant="electric"
          className="flex w-full max-w-none justify-between"
        >
          <span>RETURN TO HOMEPAGE</span>
          <Image
            alt="arrow"
            src={arrow}
            style={{ objectFit: 'contain' }}
            className="pb-1"
          />
        </Button>
      </Link>
    </OnboardingTitle>
  );
};

export default RejectedPriorConditionsPage;
