'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import InputList from '@/components/onboarding/InputList';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { FormControl } from '@willow/ui/base/form';

const schema = z.object({
  medicalConditions: z.array(z.string()).optional(),
});

type FormType = z.infer<typeof schema>;

const MedicalConditionsQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const initialConditions = useMemo(() => {
    const selections = getOnboardingFieldValue<string[] | undefined>(
      onboarding,
      'medicalConditions',
    );
    if (Array.isArray(selections) && selections.length > 0) {
      return [...selections];
    }

    return [] as string[];
  }, [onboarding]);

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      medicalConditions: initialConditions,
    },
  });

  useEffect(() => {
    if (!onboarding) return;
    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  useEffect(() => {
    const nextValues = initialConditions.length > 0 ? initialConditions : [];
    const currentValues = form.getValues('medicalConditions') ?? [];

    const valuesMatch =
      currentValues.length === nextValues.length &&
      currentValues.every((value) => nextValues.includes(value));

    if (!valuesMatch) {
      form.setValue('medicalConditions', nextValues);
    }
  }, [form, initialConditions]);

  const handleUpdate = useCallback(
    (values: string[]) => {
      form.setValue('medicalConditions', values, {
        shouldDirty: true,
        shouldTouch: true,
      });
    },
    [form],
  );

  if (!onboarding) return null;

  const hasNext = (onboarding.events || []).includes('next');

  const onSubmit = async ({ medicalConditions }: FormType) => {
    setIsSubmitting(true);

    if (!hasNext) {
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await dispatch({
        event: 'next',
        value: { medicalConditions: medicalConditions ?? [] },
      });

      const { validationError } = response;
      if (validationError?.errors?.length) {
        validationError.errors.forEach(
          (errorItem: { field: string; message: string }) => {
            if (errorItem.field === 'medicalConditions') {
              form.setError('medicalConditions', {
                message: errorItem.message,
              });
            }
          },
        );
        setIsSubmitting(false);
        return;
      }

      if (validationError?.message) {
        form.setError('medicalConditions', {
          message: validationError.message,
        });
        setIsSubmitting(false);
        return;
      }
    } catch (error: any) {
      form.setError('medicalConditions', {
        message: error?.response?.data?.message || 'Something went wrong',
      });
      setIsSubmitting(false);
      return;
    }
  };

  const selections = form.watch('medicalConditions') ?? [];

  return (
    <OnboardingTitle
      title={
        onboarding?.content?.title ||
        'What medical conditions do you currently have?'
      }
      subtitle={
        onboarding?.content?.subtitle ||
        "Your medical history helps us determine if you're eligible for Willow's program."
      }
    >
      <InputList
        list={initialConditions}
        update={handleUpdate}
        name="Medical Condition"
      />

      <FormLoader isLoading={isSubmitting || isDispatching}>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
            <FormField
              control={form.control}
              name="medicalConditions"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input {...field} type="hidden" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={!hasNext || isSubmitting || isDispatching}
            >
              <span>{selections.length ? 'CONTINUE' : 'NONE'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </form>
        </Form>
      </FormLoader>
    </OnboardingTitle>
  );
};

export default MedicalConditionsQuestionnaire;
