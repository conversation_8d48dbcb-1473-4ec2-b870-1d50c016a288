'use client';

import { useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import InputField from '@/components/ui/InputField';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  weight: z.preprocess(
    (val) => (val === '' ? undefined : val),
    z.coerce
      .number({ message: 'Invalid Number' })
      .min(10, { message: 'Invalid Number' })
      .max(1400, { message: 'Invalid Number' }),
  ),
});

type FormValues = z.infer<typeof schema>;

const WeightQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!onboarding) return;

    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  const defaultWeight = useMemo(
    () => getOnboardingFieldValue<number | undefined>(onboarding, 'weight'),
    [onboarding],
  );

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      weight: defaultWeight ?? undefined,
    },
    shouldFocusError: true,
  });

  useEffect(() => {
    if (defaultWeight === undefined) return;

    const currentWeight = form.getValues('weight');

    if (currentWeight === defaultWeight) {
      return;
    }

    form.setValue('weight', defaultWeight ?? 0, {
      shouldDirty: false,
      shouldTouch: false,
    });
  }, [defaultWeight, form]);

  if (!onboarding) return null;

  const hasNext = (onboarding.events || []).includes('next');

  const onSubmit = async ({ weight }: FormValues) => {
    form.clearErrors();
    setIsSubmitting(true);

    if (!hasNext) {
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await dispatch({ event: 'next', value: { weight } });

      const { validationError } = response;
      if (validationError?.errors?.length) {
        validationError.errors.forEach(({ field, message }) => {
          if (field === 'weight') {
            form.setError('weight', { message });
          }
        });
        setIsSubmitting(false);
        return;
      }

      if (validationError?.message) {
        form.setError('weight', { message: validationError.message });
        setIsSubmitting(false);
        return;
      }
    } catch (error: unknown) {
      const maybeError = error as {
        response?: { data?: { message?: string } };
      };
      form.setError('weight', {
        message: maybeError?.response?.data?.message ?? 'Something went wrong',
      });
      setIsSubmitting(false);
      return;
    }
  };

  return (
    <OnboardingTitle
      title={onboarding.content?.title ?? 'What is your weight?'}
      subtitle={
        onboarding.content?.subtitle ??
        "Your body composition helps us determine if you're eligible for Willow's program."
      }
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col gap-10"
        >
          <FormLoader isLoading={isSubmitting || isDispatching}>
            <InputField
              control={form.control}
              name="weight"
              type="number"
              placeholder="Pounds"
              variant="dark"
              tooltip="Please enter a numerical value"
              measure="Lbs"
              autoFocus={true}
            />

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={!hasNext || isSubmitting || isDispatching}
            >
              <span>{onboarding.content?.buttonText ?? 'CONTINUE'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default WeightQuestionnaire;
