'use client';

import { useEffect } from 'react';
import RadioGroupQuestion from '@/components/onboarding/RadioGroupQuestion';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';

const UsingGLP1Questionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);

  useEffect(() => {
    if (!onboarding) return;

    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  if (!onboarding) return null;

  return (
    <RadioGroupQuestion
      onboarding={onboarding}
      fieldName="usingGLP1"
      options={[
        { value: 'yes', label: 'Yes' },
        { value: 'no', label: 'No' },
      ]}
      fallbackTitle="Are you currently using Semaglutide or other GLP-1 medication?"
      fallbackSubtitle={`For example Dulaglutide (Trulicity), Exenatide (Bydureon Bcise, Byetta), Semaglutide (Ozempic, Wegovy, Rybelsus), Liraglutide (Victoza, Saxenda), Lixisenatide (Adlyxin) or Tirzepatide (Mounjaro).`}
      isBusy={isDispatching}
      onSubmit={({ event }) => dispatch({ event })}
    />
  );
};

export default UsingGLP1Questionnaire;
