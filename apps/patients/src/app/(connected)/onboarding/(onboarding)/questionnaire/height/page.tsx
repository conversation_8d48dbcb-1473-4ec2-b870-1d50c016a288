'use client';

import { useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import InputField from '@/components/ui/InputField';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  feet: z.preprocess(
    (val) => (val === '' ? undefined : val),
    z.coerce
      .number({ message: 'Invalid Number' })
      .min(2, { message: 'Invalid Number' })
      .max(8, { message: 'Invalid Number' }),
  ),
  inches: z.preprocess(
    (val) => (val === '' ? undefined : val),
    z.coerce
      .number({ message: 'Invalid Number' })
      .min(0, { message: 'Invalid Number' })
      .max(12, { message: 'Invalid Number' }),
  ),
});

type FormValues = z.infer<typeof schema>;

const HeightQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!onboarding) return;

    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  const height = useMemo(() => {
    return getOnboardingFieldValue<number | undefined>(onboarding, 'height');
  }, [onboarding]);

  const defaultFeet = height ? Math.floor(height / 12) : undefined;
  const defaultInches = height ? height % 12 : undefined;

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      feet: defaultFeet,
      inches: defaultInches,
    },
    shouldFocusError: true,
  });

  useEffect(() => {
    if (defaultFeet === undefined && defaultInches === undefined) {
      return;
    }

    const currentFeet = form.getValues('feet');
    const currentInches = form.getValues('inches');

    if (currentFeet === defaultFeet && currentInches === defaultInches) {
      return;
    }

    if (defaultFeet !== undefined) {
      form.setValue('feet', defaultFeet, {
        shouldDirty: false,
        shouldTouch: false,
      });
    }
    if (defaultInches !== undefined) {
      form.setValue('inches', defaultInches, {
        shouldDirty: false,
        shouldTouch: false,
      });
    }
  }, [defaultFeet, defaultInches, form]);

  if (!onboarding) return null;

  const hasNext = (onboarding.events || []).includes('next');

  const onSubmit = async ({ feet, inches }: FormValues) => {
    form.clearErrors();
    setIsSubmitting(true);

    if (!hasNext) {
      setIsSubmitting(false);
      return;
    }

    const totalInches = (feet ?? 0) * 12 + (inches ?? 0);

    try {
      const response = await dispatch({
        event: 'next',
        value: { height: totalInches },
      });

      const validationError = response.validationError;
      if (validationError?.errors?.length) {
        validationError.errors.forEach(({ field, message }) => {
          if (field === 'height') {
            form.setError('feet', { message });
          }
        });
        setIsSubmitting(false);
        return;
      }

      if (validationError?.message) {
        form.setError('feet', { message: validationError.message });
        setIsSubmitting(false);
        return;
      }
    } catch (error: unknown) {
      const maybeError = error as {
        response?: { data?: { message?: string } };
      };
      form.setError('feet', {
        message: maybeError?.response?.data?.message ?? 'Something went wrong',
      });
      setIsSubmitting(false);
      return;
    }
  };

  return (
    <OnboardingTitle
      title={onboarding.content?.title ?? 'What is your height?'}
      subtitle={
        onboarding.content?.subtitle ??
        "Your body composition helps us determine if you're eligible for Willow's program."
      }
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col items-center gap-10"
        >
          <FormLoader isLoading={isSubmitting || isDispatching}>
            <div className="flex w-full flex-col gap-5">
              <InputField
                control={form.control}
                name="feet"
                type="number"
                placeholder="Feet"
                variant="dark"
                tooltip="Please enter a numerical value"
                measure="Ft"
                autoFocus={true}
              />
              <InputField
                control={form.control}
                name="inches"
                type="number"
                placeholder="Inches"
                variant="dark"
                tooltip="Please enter a numerical value"
                measure="In"
              />
            </div>

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={!hasNext || isSubmitting || isDispatching}
            >
              <span>{onboarding.content?.buttonText ?? 'CONTINUE'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default HeightQuestionnaire;
