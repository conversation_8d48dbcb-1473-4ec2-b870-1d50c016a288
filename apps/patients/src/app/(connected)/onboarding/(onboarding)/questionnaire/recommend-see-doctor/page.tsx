'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';

const RecommendSeeDoctorPage = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm({});

  useEffect(() => {
    if (!onboarding) return;
    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  if (!onboarding) return null;

  const availableEvents = onboarding.events || [];
  const hasNext = availableEvents.includes('next');

  const onSubmit = async () => {
    setIsSubmitting(true);

    if (!hasNext) {
      setIsSubmitting(false);
      return;
    }

    try {
      await dispatch({ event: 'next' });
    } catch (error) {
      console.error(
        'Failed to continue from recommend-see-doctor screen',
        error,
      );
      setIsSubmitting(false);
    }
  };

  return (
    <OnboardingTitle
      title={
        onboarding?.content?.title ||
        'We recommend that you see a doctor at least every other year.'
      }
      subtitle={onboarding?.content?.subtitle || ''}
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col items-center gap-6"
        >
          <FormLoader isLoading={isSubmitting || isDispatching}>
            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={!hasNext || isSubmitting || isDispatching}
            >
              <span>{onboarding?.content?.buttonText || 'CONTINUE'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default RecommendSeeDoctorPage;
