'use client';

import { useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import InputList from '@/components/onboarding/InputList';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { onboardingStepsAtom } from '@/store/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { FormControl } from '@willow/ui/base/form';

const schema = z.object({
  medications: z.array(z.string()).optional(),
});

type FormType = z.infer<typeof schema>;

const MedicationsQuestionnaire = () => {
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const existingMedications = useMemo(() => {
    const selections = getOnboardingFieldValue<string[] | undefined>(
      onboarding,
      'medications',
    );
    return Array.isArray(selections) ? selections : [];
  }, [onboarding]);

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      medications: existingMedications,
    },
  });

  useEffect(() => {
    if (!onboarding) return;
    setSteps({
      stepName: onboarding.stepName || 'Health Questionnaire',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  const handleUpdate = (values: string[]) => {
    form.setValue('medications', values);
  };

  if (!onboarding) return null;

  const hasNext = (onboarding.events || []).includes('next');

  const onSubmit = async ({ medications }: FormType) => {
    setIsSubmitting(true);

    if (!hasNext) {
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await dispatch({
        event: 'next',
        value: { medications: medications ?? [] },
      });

      const { validationError } = response;
      if (validationError?.errors?.length) {
        validationError.errors.forEach(
          (errorItem: { field: string; message: string }) => {
            if (errorItem.field === 'medications') {
              form.setError('medications', { message: errorItem.message });
            }
          },
        );
        setIsSubmitting(false);
        return;
      }

      if (validationError?.message) {
        form.setError('medications', { message: validationError.message });
        setIsSubmitting(false);
        return;
      }
    } catch (error: any) {
      form.setError('medications', {
        message: error?.response?.data?.message || 'Something went wrong',
      });
      setIsSubmitting(false);
      return;
    }
  };

  const selections = form.watch('medications') ?? [];

  return (
    <OnboardingTitle
      title={
        onboarding?.content?.title || 'What medications are you currently on?'
      }
      subtitle={
        onboarding?.content?.subtitle ||
        "Your medical history helps us determine if you're eligible for Willow's program."
      }
    >
      <FormLoader isLoading={isSubmitting || isDispatching}>
        <InputList
          list={existingMedications}
          update={handleUpdate}
          name="Medication"
        />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
            <FormField
              control={form.control}
              name="medications"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input {...field} type="hidden" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={!hasNext || isSubmitting || isDispatching}
            >
              <span>{selections.length ? 'CONTINUE' : 'NONE'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </form>
        </Form>
      </FormLoader>
    </OnboardingTitle>
  );
};

export default MedicationsQuestionnaire;
