'use client';

import { useEffect, useRef, useState } from 'react';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import { InfoDialog } from '@/components/ui/infoDialog';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { cn } from '@/lib/utils';
import { onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';

import { useTracking } from '~/analytics/use-tracking';

interface FormData {
  digit1: string;
  digit2: string;
  digit3: string;
  digit4: string;
}

const SSNCheck = () => {
  const analyticsData = useAnalyticsData();
  const { track } = useTracking();
  const form = useForm<FormData>();
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);

  const inputRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];

  const [digits, setDigits] = useState(['', '', '', '']);
  const [showInfoDialog, setShowInfoDialog] = useState(false);
  const [activeSubmission, setActiveSubmission] = useState<
    'submit' | 'skip' | null
  >(null);

  useEffect(() => {
    if (!onboarding) return;

    setSteps({
      stepName: onboarding.stepName || 'Identity Verification',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  useEffect(() => {
    if (!onboarding) return;

    const lastFourSSN = getOnboardingFieldValue<string | undefined>(
      onboarding,
      'lastFourSSN',
    );

    if (lastFourSSN) {
      const existingDigits = lastFourSSN.split('');
      setDigits(existingDigits);
      existingDigits.forEach((digit, index) => {
        form.setValue(`digit${index + 1}` as keyof FormData, digit);
      });

      const focusIndex = existingDigits.length === 4 ? 3 : 0;
      setTimeout(() => {
        inputRefs[focusIndex]?.current?.focus();
      }, 0);
      return;
    }

    setDigits(['', '', '', '']);
    ['digit1', 'digit2', 'digit3', 'digit4'].forEach((name) =>
      form.setValue(name as keyof FormData, ''),
    );
    setTimeout(() => {
      inputRefs[0]?.current?.focus();
    }, 0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form, onboarding]);

  const hasSubmitEvent = onboarding?.events?.includes('submit') ?? false;
  const hasSkipEvent = onboarding?.events?.includes('skip') ?? false;

  const handleDigitChange = (index: number, value: string) => {
    if (!/^\d?$/.test(value)) return;

    const newDigits = [...digits];
    newDigits[index] = value;
    setDigits(newDigits);
    form.setValue(`digit${index + 1}` as keyof FormData, value);
    form.clearErrors('digit1');

    if (value && index < inputRefs.length - 1) {
      inputRefs[index + 1]?.current?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !digits[index] && index > 0) {
      inputRefs[index - 1]?.current?.focus();
    }
  };

  const handleValidationError = (validationError?: {
    message?: string;
    errors?: { field: string; message: string }[];
  }) => {
    if (!validationError) return false;

    if (validationError.errors?.length) {
      const ssnError = validationError.errors.find(
        (errorItem) => errorItem.field === 'lastFourSSN',
      );
      if (ssnError?.message) {
        form.setError('digit1', { message: ssnError.message });
        return true;
      }
    }

    if (validationError.message) {
      form.setError('digit1', { message: validationError.message });
      return true;
    }

    return false;
  };

  const onSubmit = async () => {
    if (!hasSubmitEvent) {
      form.setError('digit1', {
        message:
          'Submitting SSN is currently unavailable. Please try again later.',
      });
      return;
    }

    const lastFourSSN = digits.join('');
    if (lastFourSSN.length !== 4) {
      form.setError('digit1', { message: 'Please enter all 4 digits' });
      return;
    }

    form.clearErrors('digit1');
    setActiveSubmission('submit');

    try {
      track('SSN Verification Attempted', analyticsData);
      const response = await dispatch({
        event: 'submit',
        value: { lastFourSSN },
      });

      if (handleValidationError(response.validationError)) {
        setActiveSubmission(null);
        return;
      }
    } catch (error) {
      const maybeError = error as {
        response?: { data?: { message?: string } };
      };
      form.setError('digit1', {
        message:
          maybeError?.response?.data?.message ??
          'Verification failed. Please try again.',
      });
      setActiveSubmission(null);
    }
  };

  const onSkip = async () => {
    if (!hasSkipEvent) {
      form.setError('digit1', {
        message:
          'Skipping SSN entry is currently unavailable. Please try again later.',
      });
      return;
    }

    form.clearErrors('digit1');
    setActiveSubmission('skip');

    try {
      track('SSN Verification Skipped', analyticsData);
      const response = await dispatch({ event: 'skip' });

      if (handleValidationError(response.validationError)) {
        setActiveSubmission(null);
        return;
      }
    } catch (error) {
      const maybeError = error as {
        response?: { data?: { message?: string } };
      };
      form.setError('digit1', {
        message:
          maybeError?.response?.data?.message ??
          'Skip failed. Please try again.',
      });
      setActiveSubmission(null);
    }
  };

  if (!onboarding) return null;

  const isLoading = isDispatching || activeSubmission !== null;
  const isContinueDisabled =
    digits.some((digit) => !digit) || isLoading || !hasSubmitEvent;

  return (
    <OnboardingTitle
      title={
        onboarding?.content?.title ||
        'Please provide the last 4 digits of your social security number'
      }
      subtitle={
        onboarding?.content?.subtitle ||
        'Enter the last 4 digits of your Social Security Number'
      }
    >
      <button
        className="cursor-pointer text-center text-xl text-white underline hover:opacity-80"
        onClick={() => setShowInfoDialog(true)}
        type="button"
      >
        Why do we need this?
      </button>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full space-y-8"
        >
          <div className="flex justify-center gap-4">
            {inputRefs.map((ref, index) => (
              <input
                key={index}
                ref={ref}
                type="tel"
                inputMode="numeric"
                pattern="[0-9]"
                maxLength={1}
                value={digits[index]}
                onChange={(e) => handleDigitChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                className={cn(
                  'h-16 w-16 rounded-lg border-2 bg-transparent text-center text-2xl text-white focus:border-electric focus:outline-none',
                  digits[index] ? 'border-electric' : 'border-white',
                )}
              />
            ))}
          </div>

          <div className="text-center text-white">
            <span>Don't want to provide this?</span>
            <br />
            <button
              type="button"
              onClick={onSkip}
              className="text-white underline hover:text-yellow-400"
              disabled={isLoading || !hasSkipEvent}
            >
              upload your ID and a photo
            </button>
            <span> instead.</span>
          </div>

          <div className="text-center text-sm text-white">
            256-BIT TLS SECURITY
          </div>

          <FormLoader isLoading={isLoading}>
            <Button
              size={'lg'}
              variant={'electric'}
              className="w-full max-w-none"
              type="submit"
              disabled={isContinueDisabled}
              style={{
                backgroundColor: isContinueDisabled ? '#6b7280' : undefined,
                cursor: isContinueDisabled ? 'not-allowed' : 'pointer',
              }}
            >
              {onboarding?.content?.buttonText || 'VERIFY'}
            </Button>
          </FormLoader>
        </form>
      </Form>

      {showInfoDialog && (
        <InfoDialog
          title="Why do we need the last 4 digits of your SSN?"
          confirmBtnText="I UNDERSTAND"
          confirmBtnClick={() => setShowInfoDialog(false)}
          closeBtnClick={() => setShowInfoDialog(false)}
          variant="dark"
        >
          <div className="text-center text-xl font-normal text-white">
            The last 4 digits of your social security number help us confirm
            your identity. Your personal identifiable information, medical
            history, and ID will remain confidential within our secure platform.
            This is an optional step. If you prefer, you can verify your
            identity by uploading a photo and your ID.
          </div>
        </InfoDialog>
      )}
    </OnboardingTitle>
  );
};

export default SSNCheck;
