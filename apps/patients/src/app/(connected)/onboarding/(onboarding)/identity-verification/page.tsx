'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import { InfoDialog } from '@/components/ui/infoDialog';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingStateMachine } from '@/hooks/useOnboardingStateMachine';
import { onboardingStepsAtom } from '@/store/store';
import { useSetAtom } from 'jotai';
import { useForm } from 'react-hook-form';

import { useTracking } from '~/analytics/use-tracking';

const IdentityVerification = () => {
  const analyticsData = useAnalyticsData();
  const { track } = useTracking();
  const form = useForm();
  const {
    data: onboarding,
    dispatch,
    isDispatching,
  } = useOnboardingStateMachine();
  const setSteps = useSetAtom(onboardingStepsAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showInfoDialog, setShowInfoDialog] = useState(false);

  useEffect(() => {
    if (!onboarding) return;

    setSteps({
      stepName: onboarding.stepName || 'Identity Verification',
      percentage: onboarding.percentage || 0,
    });
  }, [onboarding, setSteps]);

  const hasNextEvent = onboarding?.events?.includes('next') ?? false;

  const onSubmit = async () => {
    if (!hasNextEvent) {
      form.setError('unsure', {
        message:
          'Continue option is currently unavailable. Please refresh and try again.',
      });
      return;
    }

    form.clearErrors();
    setIsSubmitting(true);

    try {
      track('Identity Verification Started', analyticsData);
      const response = await dispatch({ event: 'next' });
      const { validationError } = response;

      if (validationError?.errors?.length) {
        const [firstError] = validationError.errors;
        if (firstError?.message) {
          form.setError('unsure', { message: firstError.message });
        }
        setIsSubmitting(false);
        return;
      }

      if (validationError?.message) {
        form.setError('unsure', { message: validationError.message });
        setIsSubmitting(false);
        return;
      }
    } catch (error) {
      const maybeError = error as {
        response?: { data?: { message?: string } };
      };
      form.setError('unsure', {
        message:
          maybeError?.response?.data?.message ??
          'Something went wrong. Please try again.',
      });
      setIsSubmitting(false);
    }
  };

  if (!onboarding) return null;

  const isLoading = isSubmitting || isDispatching;

  return (
    <OnboardingTitle
      title={
        onboarding?.content?.title ||
        'Help us verify your identity so a doctor can legally prescribe you medication'
      }
      subtitle={
        onboarding?.content?.subtitle ||
        'To continue with treatment, we need to verify your identity.'
      }
    >
      <button
        className="cursor-pointer text-center text-xl text-white underline hover:opacity-80"
        onClick={() => setShowInfoDialog(true)}
        type="button"
      >
        Why do you need to identify my identity?
      </button>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
          <FormLoader isLoading={isLoading}>
            <Button
              size={'lg'}
              variant={'electric'}
              className="flex w-full max-w-none justify-between"
              type={'submit'}
              disabled={isLoading || !hasNextEvent}
            >
              <span>{onboarding?.content?.buttonText || 'CONTINUE'}</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>

      {showInfoDialog && (
        <InfoDialog
          title="Why do you need to identify my identity?"
          confirmBtnText={
            <div className="flex w-full items-center justify-between text-base lg:text-lg">
              <span>ACCEPT AND CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </div>
          }
          confirmBtnClick={() => {
            setShowInfoDialog(false);
            form.handleSubmit(onSubmit)();
          }}
          closeBtnClick={() => {
            setShowInfoDialog(false);
          }}
          variant="dark"
        >
          <div className="text-center text-xl font-normal text-white">
            A medical provider is required to verify your identity before
            prescribing medication. Your personal identifiable information,
            medical history, and ID will remain confidential within our secure
            platform.
          </div>
        </InfoDialog>
      )}
    </OnboardingTitle>
  );
};

export default IdentityVerification;
