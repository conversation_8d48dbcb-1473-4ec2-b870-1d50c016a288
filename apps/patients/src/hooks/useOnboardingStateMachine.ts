import type { OnboardingData } from '@/data/types';
import { useCallback, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { onboardingDataAtom } from '@/store/store';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSetAtom } from 'jotai';

import { apiClient } from '@willow/utils/api/client';

interface DispatchPayload {
  type?: string;
  event?: string;
  value?: unknown;
}

export const ONBOARDING_STATE_QUERY_KEY = ['onboarding', 'state'] as const;

export const useOnboardingStateMachine = (options?: { enabled?: boolean }) => {
  const setOnboardingData = useSetAtom(onboardingDataAtom);
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();

  const applySnapshot = useCallback(
    (snapshot: OnboardingData) => {
      queryClient.setQueryData(ONBOARDING_STATE_QUERY_KEY, snapshot);
      setOnboardingData(snapshot);
    },
    [queryClient, setOnboardingData],
  );

  const query = useQuery({
    queryKey: ONBOARDING_STATE_QUERY_KEY,
    queryFn: async () => {
      // Extract discount query params from URL
      const promo = searchParams.get('promo');
      const refcode = searchParams.get('refcode');

      // Build query string for discount params
      const queryString = new URLSearchParams();
      if (promo) queryString.append('promo', promo);
      if (refcode) queryString.append('refcode', refcode);
      const queryUrl = queryString.toString()
        ? `?${queryString.toString()}`
        : '';

      const response = await apiClient.get<OnboardingData>(
        `/onboarding/state${queryUrl}`,
      );
      return response.data;
    },
    enabled: options?.enabled ?? true,
  });

  useEffect(() => {
    if (query.data) {
      applySnapshot(query.data);
    }
  }, [applySnapshot, query.data]);

  const { mutateAsync: dispatchMutation, isPending: isDispatching } =
    useMutation({
      mutationFn: async (payload: DispatchPayload) => {
        const type = payload.type ?? payload.event;
        if (!type) {
          throw new Error('Dispatch payload requires a type or event.');
        }

        const response = await apiClient.post<OnboardingData>(
          '/onboarding/dispatch',
          {
            type,
            value: payload.value,
          },
        );
        return response.data;
      },
      onSuccess: (data) => {
        applySnapshot(data);
      },
    });

  const dispatch = useCallback(
    async (payload: DispatchPayload) => {
      const data = await dispatchMutation(payload);
      return data;
    },
    [dispatchMutation],
  );

  return {
    data: query.data,
    isPending: query.isPending,
    error: query.error,
    refetch: query.refetch,
    dispatch,
    isDispatching,
    applySnapshot,
  };
};
