import * as React from 'react';
import { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { isAxiosError } from 'axios';
import { FileIcon } from 'lucide-react';

import { <PERSON><PERSON> } from '@willow/ui/base/button';
import { Dialog, DialogContent, DialogTitle } from '@willow/ui/base/dialog';
import { apiClient } from '@willow/utils/api/client';

import { useProfile } from '~/hooks/profile';

const downloadDocument = (documentName: string, content: Blob) => {
  const url = window.URL.createObjectURL(content);
  const a = document.createElement('a');
  a.href = url;
  a.download = documentName;
  a.click();
  window.URL.revokeObjectURL(url);
};

const getErrorMessageFromBlobRequest = async (error: any): Promise<string> => {
  let errorMessage = 'Failed to download letter, contact patient support.';
  try {
    if (isAxiosError<Blob>(error) && error?.status == 404 && error.response) {
      const errorResponse: string = await error.response.data.text();
      const parsedError: { message: string } = JSON.parse(errorResponse);
      errorMessage = parsedError.message;
    }
    return errorMessage;
  } catch (newError: any) {
    console.log(newError);
    return errorMessage;
  }
};

const CardDownloadHsaDoc = () => {
  const [open, setOpenDownloadDialog] = useState(false); // Cambiar a useState para manejar el estado reactivo del modal
  return (
    <div className="w-full">
      <CardDownloadHsaDocDialog open={open} setOpen={setOpenDownloadDialog} />
      <button
        type="button"
        onClick={() => setOpenDownloadDialog(true)}
        className="flex h-auto w-full justify-between rounded-2xl border-none bg-white p-4 transition hover:scale-[1.01] hover:bg-white hover:shadow-lg md:p-6"
      >
        <div className="flex w-full flex-col gap-2">
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-2 md:gap-4">
              <div className="flex aspect-square h-[32px] w-[32px] items-center justify-center rounded-md bg-[#2F4C781A] !bg-opacity-0 md:h-[48px] md:w-[48px]">
                <FileIcon
                  color="#2F4C78"
                  className="h-[16px] w-[16px] md:h-[24px] md:w-[24px]"
                />
              </div>

              <span className="w-full text-wrap text-left text-xl font-medium leading-normal text-denim">
                HSA Receipt
              </span>
            </div>
            <div className="rounded-full bg-denim px-4 py-2.5 text-[13px] font-semibold !leading-none text-white md:px-6 md:py-4 md:text-lg">
              DOWNLOAD
            </div>
          </div>
        </div>
      </button>
    </div>
  );
};

// Letter of Medical Necessity download card
const CardDownloadLetterOfMedicalNecessity = () => {
  const profile = useProfile();
  const [errorMessage, setErrorMessage] = useState('');
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  const [unavailableReason, setUnavailableReason] = useState<string | null>(
    null,
  );
  React.useEffect(() => {
    const checkAvailability = async () => {
      setLoading(true);
      try {
        const response = await apiClient.get(
          '/patient/insurance/letter-of-medical-necessity/check-availability',
        );
        if (typeof response.data === 'object' && response.data !== null) {
          setIsAvailable(!!response.data.available);
          setUnavailableReason(
            response.data.available === false &&
              typeof response.data.reason === 'string'
              ? String(response.data.reason)
              : null,
          );
        } else {
          setIsAvailable(false);
          setUnavailableReason('Unknown error');
        }
      } catch {
        setIsAvailable(false);
        setUnavailableReason('Error checking availability');
      } finally {
        setLoading(false);
      }
    };
    void checkAvailability();
  }, []);

  const handleDownloadLetterOfMedicalNecessity = async () => {
    try {
      const response = await apiClient.get(
        `/patient/insurance/letter-of-medical-necessity`,
        {
          responseType: 'blob',
        },
      );

      const document: Blob = response.data as Blob;
      downloadDocument(
        `willow-letter_of_medical_necessity-${profile.user.email}.pdf`,
        document,
      );
    } catch (error: any) {
      const errorMessage = await getErrorMessageFromBlobRequest(error);
      setErrorMessage(errorMessage);
    }
  };

  return (
    <div className="w-full">
      <button
        type="button"
        className="flex h-auto w-full justify-between rounded-2xl border-none bg-white p-4 transition hover:scale-[1.01] hover:bg-white hover:shadow-lg md:p-6"
        onClick={handleDownloadLetterOfMedicalNecessity}
        disabled={loading || isAvailable === false}
        aria-disabled={loading || isAvailable === false}
      >
        <div className="flex w-full flex-col gap-2">
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-2 md:gap-4">
              <div className="flex aspect-square h-[32px] w-[32px] items-center justify-center rounded-md bg-[#2F4C781A] !bg-opacity-0 md:h-[48px] md:w-[48px]">
                <FileIcon
                  color="#2F4C78"
                  className="h-[16px] w-[16px] md:h-[24px] md:w-[24px]"
                />
              </div>

              <span className="w-full text-wrap text-left text-xl font-medium leading-normal text-denim">
                Letter of Medical Necessity
              </span>
            </div>
            <div
              style={
                loading || isAvailable === false
                  ? { opacity: 0.5, cursor: 'not-allowed' }
                  : {}
              }
              className="rounded-full bg-denim px-4 py-2.5 text-[13px] font-semibold !leading-none text-white md:px-6 md:py-4 md:text-lg"
            >
              DOWNLOAD
            </div>
          </div>
          {errorMessage && (
            <div className="w-full text-left">
              <span className="text-wrap text-xs text-denim text-red-500 md:text-sm">
                {errorMessage}
              </span>
            </div>
          )}
          {!loading && isAvailable === false && unavailableReason && (
            <div className="w-full text-left">
              <span className="text-wrap text-xs text-red-500 md:text-sm">
                {unavailableReason}
              </span>
            </div>
          )}
        </div>
      </button>
    </div>
  );
};

interface CardDownloadLinkDialogProps {
  open: boolean;
  setOpen: (value: boolean) => void;
}

const CardDownloadHsaDocDialog = ({
  setOpen,
  open,
}: CardDownloadLinkDialogProps) => {
  const profile = useProfile();
  const firstYear = new Date(profile.createdAt).getFullYear() || 2020;
  const [year, setYear] = useState(firstYear);
  const [errorMessage, setErrorMessage] = useState('');
  const [hsaYears, setHsaYears] = useState<number[]>([]);
  const handleDownloadReceipt = async () => {
    try {
      const response = await apiClient.get(
        `/patient/insurance/hsa-receipt/${year}`,
        { responseType: 'blob' },
      );

      setErrorMessage('');

      const document: Blob = response.data as Blob;
      downloadDocument(
        `willow-hsa_receipt-${profile.user.email}-${year}.pdf`,
        document,
      );
    } catch {
      setErrorMessage('Failed to download receipt, contact patient support.');
    }
  };

  React.useEffect(() => {
    const currentYear = new Date().getFullYear();
    const years = Array.from(
      { length: currentYear - firstYear + 1 },
      (_, i) => firstYear + i,
    );

    setHsaYears(years);
  }, [firstYear]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-[90%] p-4 md:max-w-[700px] md:p-8">
        <div className="flex flex-col gap-4 sm:flex-row md:flex-row md:gap-16">
          <DialogTitle asChild>
            <span className="text-[26px] !font-medium text-denim">
              Download HSA Document
            </span>
          </DialogTitle>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              void handleDownloadReceipt();
            }}
            className="flex w-full flex-col"
          >
            <label className="mb-6 text-base font-medium text-denim md:mb-8">
              for{' '}
              <span className="font-bold text-denim">
                {' '}
                {profile.user.firstName} {profile.user.lastName}
              </span>
            </label>
            <Select
              onValueChange={(value) => setYear(Number(value))}
              value={year.toString()}
            >
              <SelectTrigger className="mb-8 w-full rounded-none border-b border-gray-300 p-2 text-base text-denim-darker">
                <SelectValue placeholder="State" />
              </SelectTrigger>
              <SelectContent className="py-0">
                {hsaYears.map((year) => (
                  <SelectItem
                    key={year}
                    value={year.toString()}
                    className="h-auto cursor-pointer py-4 text-base text-denim-darker"
                  >
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex flex-col gap-4 md:flex-row">
              <Button
                onClick={() => setOpen(false)}
                variant="denimOutline"
                className="min-h-12 w-full"
                type="button"
                size="sm"
              >
                CANCEL
              </Button>
              <Button
                variant="denim"
                className="min-h-12 w-full text-white"
                type="submit"
                size="sm"
              >
                DOWNLOAD RECEIPT
              </Button>
            </div>
          </form>
        </div>
        {errorMessage && (
          <div className="w-full text-left">
            <span className="text-wrap text-xs text-denim text-red-500 md:text-sm">
              {errorMessage}
            </span>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
CardDownloadHsaDocDialog.displayName = 'CardDownloadLinkDialog';
CardDownloadHsaDoc.displayName = 'CardDownloadHsaDoc';
CardDownloadLetterOfMedicalNecessity.displayName =
  'CardDownloadLetterOfMedicalNecessity';
export { CardDownloadHsaDoc, CardDownloadLetterOfMedicalNecessity };
