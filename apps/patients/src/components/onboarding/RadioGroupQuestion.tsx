'use client';

import type { OnboardingData } from '@/data/types';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { useForm } from 'react-hook-form';

interface RadioOption {
  value: string;
  label: string;
}

interface RadioGroupQuestionProps {
  onboarding: OnboardingData;
  fieldName: string;
  options: RadioOption[];
  fallbackTitle: string;
  fallbackSubtitle?: string | string[];
  fallbackButtonText?: string;
  errorMessage?: string;
  valueAliases?: string[];
  eventResolver?: (value: string) => string;
  autoSubmitOnSelect?: boolean;
  isBusy?: boolean;
  onSubmit: (params: {
    value: string;
    event: string;
  }) => Promise<OnboardingData>;
  onSuccess?: (params: { value: string; response: OnboardingData }) => void;
}

const unavailableSelectionMessage =
  'Selection currently unavailable. Please try again.';

interface FormValues {
  value: string;
}

const RadioGroupQuestion = ({
  onboarding,
  fieldName,
  options,
  fallbackTitle,
  fallbackSubtitle,
  fallbackButtonText = 'CONTINUE',
  errorMessage = 'Please select an option',
  valueAliases = [],
  eventResolver = (value) => value,
  autoSubmitOnSelect = false,
  isBusy = false,
  onSubmit,
  onSuccess,
}: RadioGroupQuestionProps) => {
  const optionValues = useMemo(
    () => options.map((option) => option.value),
    [options],
  );

  const selectedValue = getOnboardingFieldValue<string | undefined>(
    onboarding,
    fieldName,
    {
      aliases: valueAliases,
    },
  );

  const hasPreselectedDataRef = useRef<boolean>(Boolean(selectedValue));
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    defaultValues: {
      value: selectedValue ?? '',
    },
    shouldFocusError: true,
  });

  useEffect(() => {
    form.setValue('value', selectedValue ?? '', {
      shouldDirty: false,
      shouldTouch: false,
    });

    if (selectedValue) {
      hasPreselectedDataRef.current = true;
    }
  }, [form, selectedValue]);

  const availableEvents = useMemo(
    () => onboarding.events ?? [],
    [onboarding.events],
  );
  const title = onboarding.content?.title ?? fallbackTitle;
  const subtitle = onboarding.content?.subtitle ?? fallbackSubtitle;
  const buttonText = onboarding.content?.buttonText ?? fallbackButtonText;
  const isLoading = isSubmitting || isBusy;

  const submitHandler = useCallback(
    async ({ value }: FormValues) => {
      form.clearErrors('value');

      if (!value) {
        form.setError('value', { message: errorMessage });
        return;
      }

      if (!optionValues.includes(value)) {
        form.setError('value', { message: errorMessage });
        return;
      }

      const event = eventResolver(value);
      if (!availableEvents.includes(event)) {
        form.setError('value', { message: unavailableSelectionMessage });
        return;
      }

      setIsSubmitting(true);

      try {
        const response = await onSubmit({ value, event });
        const { validationError } = response;

        if (validationError?.errors?.length) {
          const fieldError = validationError.errors.find(
            (errorItem) => errorItem.field === fieldName,
          );

          if (fieldError?.message) {
            form.setError('value', { message: fieldError.message });
            setIsSubmitting(false);
            return;
          }
        }

        if (validationError?.message) {
          form.setError('value', { message: validationError.message });
          setIsSubmitting(false);
          return;
        }

        onSuccess?.({ value, response });
      } catch (error: unknown) {
        const maybeError = error as {
          response?: { data?: { message?: string } };
        };
        form.setError('value', {
          message:
            maybeError?.response?.data?.message ?? 'Something went wrong',
        });
        setIsSubmitting(false);
      }
    },
    [
      availableEvents,
      errorMessage,
      eventResolver,
      fieldName,
      form,
      onSubmit,
      onSuccess,
      optionValues,
    ],
  );

  const submitForm = useCallback(() => {
    return form.handleSubmit(submitHandler)();
  }, [form, submitHandler]);

  const handleFormSubmit = useCallback(
    (event: React.FormEvent<HTMLFormElement>) => {
      event.preventDefault();
      void submitForm();
    },
    [submitForm],
  );

  const handleRadioChange = useCallback(
    (value: string) => {
      form.setValue('value', value, {
        shouldDirty: true,
        shouldTouch: true,
      });
      form.clearErrors('value');

      if (!autoSubmitOnSelect || hasPreselectedDataRef.current) {
        return;
      }

      const event = eventResolver(value);
      if (!availableEvents.includes(event)) {
        return;
      }

      hasPreselectedDataRef.current = true;

      setTimeout(() => {
        void submitForm();
      }, 150);
    },
    [autoSubmitOnSelect, availableEvents, eventResolver, form, submitForm],
  );

  return (
    <OnboardingTitle title={title} subtitle={subtitle}>
      <Form {...form}>
        <form
          onSubmit={handleFormSubmit}
          className="flex w-full flex-col items-center gap-10"
        >
          <FormLoader isLoading={isLoading}>
            <FormField
              control={form.control}
              name="value"
              render={({ field }) => (
                <FormItem className="grid w-full gap-1 py-2">
                  <FormControl>
                    <RadioGroup
                      className="flex w-full flex-col items-center gap-5 md:items-start"
                      value={field.value}
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleRadioChange(value);
                      }}
                    >
                      {options.map((option, index) => {
                        const optionEvent = eventResolver(option.value);
                        const isDisabled =
                          !availableEvents.includes(optionEvent);

                        return (
                          <div
                            key={option.value}
                            className="relative flex h-20 w-full rounded-lg bg-glass"
                          >
                            <div className="ml-9 flex h-full w-full items-center">
                              <div className="flex h-full w-full items-center space-x-2">
                                <RadioGroupItem
                                  className="border-white"
                                  value={option.value}
                                  id={`${fieldName}-${index}`}
                                  disabled={isDisabled || isLoading}
                                />
                                <Label
                                  className="flex h-full w-full cursor-pointer items-center text-white"
                                  htmlFor={`${fieldName}-${index}`}
                                >
                                  {option.label}
                                </Label>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {(!autoSubmitOnSelect || hasPreselectedDataRef.current) && (
              <Button
                size="lg"
                variant="electric"
                className="flex w-full max-w-none justify-between"
                type="submit"
                disabled={isLoading}
              >
                <span>{buttonText}</span>
                <Image
                  alt="arrow"
                  src={arrow}
                  style={{ objectFit: 'contain' }}
                />
              </Button>
            )}
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default RadioGroupQuestion;
