import type { OnboardingData } from '@/data/types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLoader,
  FormMessage,
} from '@/components/ui/form';
import { getOnboardingFieldValue } from '@/lib/onboarding-routing';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

interface CheckboxOption {
  id: string;
  label: string;
}

interface CheckboxListQuestionConfig {
  fieldName: string;
  fallbackTitle: string;
  fallbackSubtitle?: string | string[];
  options: CheckboxOption[];
  noneOption?: CheckboxOption;
  noneSpacing?: boolean;
  errorMessage?: string;
  event: string;
  validationField: string;
}

interface CheckboxListQuestionProps {
  onboarding: OnboardingData;
  isBusy?: boolean;
  config: CheckboxListQuestionConfig;
  onSubmit: (params: {
    event: string;
    value: Record<string, any>;
  }) => Promise<OnboardingData>;
}

const CheckboxListQuestion = ({
  onboarding,
  isBusy = false,
  config,
  onSubmit,
}: CheckboxListQuestionProps) => {
  const {
    fieldName,
    fallbackTitle,
    fallbackSubtitle,
    options,
    noneOption,
    noneSpacing = true,
    errorMessage = 'Please select at least one option',
    event,
  } = config;

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Combine regular options with none option if provided
  const allOptions = noneOption ? [...options, noneOption] : options;
  const noneId = noneOption?.id;

  // Get existing value from onboarding data
  const existingValues = useMemo(
    () => getOnboardingFieldValue<string[]>(onboarding, fieldName),
    [onboarding, fieldName],
  );

  const schema = z.object({
    [fieldName]: z.array(z.string()).min(1, errorMessage),
  });

  type FormType = z.infer<typeof schema>;

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    values: {
      [fieldName]: existingValues,
    } as FormType,
  });

  // Get title and subtitle from onboarding content or fallback
  const title = onboarding.content?.title ?? fallbackTitle;
  const subtitle = onboarding.content?.subtitle ?? fallbackSubtitle;
  const isLoading = isSubmitting || isBusy;

  const submitHandler = useCallback(
    async (formData: FormType) => {
      form.clearErrors(fieldName as any);

      let values = formData[fieldName] || [];

      // Handle "none" option - if selected, clear other selections
      if (noneId && values.includes(noneId)) {
        values = [];
      }

      setIsSubmitting(true);

      try {
        const response = await onSubmit({
          event,
          value: { [fieldName]: values },
        });
        const { validationError } = response;

        if (validationError?.errors?.length) {
          const fieldError = validationError.errors.find(
            (errorItem) => errorItem.field === fieldName,
          );

          if (fieldError?.message) {
            form.setError(fieldName as any, { message: fieldError.message });
            setIsSubmitting(false);
            return;
          }
        }

        if (validationError?.message) {
          form.setError(fieldName as any, { message: validationError.message });
          setIsSubmitting(false);
          return;
        }
      } catch (error: unknown) {
        const maybeError = error as {
          response?: { data?: { message?: string } };
        };
        form.setError(fieldName as any, {
          message:
            maybeError?.response?.data?.message ?? 'Something went wrong',
        });
        setIsSubmitting(false);
      }
    },
    [event, fieldName, form, noneId, onSubmit],
  );

  // Check if any option is selected
  const hasSelection = form.watch(fieldName as any)?.length > 0;

  const handleFormSubmit = useCallback(
    (event: React.FormEvent<HTMLFormElement>) => {
      event.preventDefault();
      void form.handleSubmit(submitHandler)();
    },
    [form, submitHandler],
  );

  return (
    <OnboardingTitle title={title} subtitle={subtitle}>
      <Form {...form}>
        <form onSubmit={handleFormSubmit} className="flex flex-col gap-10">
          <FormLoader isLoading={isLoading}>
            <div className="gap-5">
              <FormField
                control={form.control}
                name={fieldName as any}
                render={() => (
                  <FormItem>
                    {allOptions.map((item, index) => (
                      <React.Fragment key={item.id}>
                        {/* Add spacing before the none option */}
                        {noneOption &&
                          noneSpacing &&
                          index === allOptions.length - 1 && (
                            <div className="h-12" />
                          )}
                        <FormField
                          control={form.control}
                          name={fieldName as any}
                          render={({ field }) => {
                            const currentValues = field.value || [];
                            const isChecked = currentValues.includes(item.id);

                            return (
                              <FormItem
                                className={cn(
                                  'medical',
                                  isChecked && 'bg-[#445f85]',
                                  'transition-colors hover:bg-[#445f85]',
                                )}
                              >
                                <FormControl>
                                  <div
                                    className={cn(
                                      'rounded border border-[#63799A]',
                                    )}
                                  >
                                    <label
                                      htmlFor={`${fieldName}-${item.id}`}
                                      className="flex cursor-pointer items-center p-5 text-lg text-white"
                                    >
                                      <Checkbox
                                        id={`${fieldName}-${item.id}`}
                                        checked={isChecked}
                                        onCheckedChange={(checked) => {
                                          if (checked) {
                                            if (noneId && item.id === noneId) {
                                              field.onChange([noneId]);
                                            } else {
                                              const newValue = [
                                                ...currentValues,
                                                item.id,
                                              ].filter(
                                                (value) => value !== noneId,
                                              );
                                              field.onChange(newValue);
                                            }
                                          } else {
                                            field.onChange(
                                              currentValues.filter(
                                                (value) => value !== item.id,
                                              ),
                                            );
                                          }
                                        }}
                                        className="mr-4 data-[state=checked]:bg-electric"
                                      />
                                      {item.label}
                                    </label>
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            );
                          }}
                        />
                      </React.Fragment>
                    ))}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button
              size="lg"
              variant="electric"
              className="flex w-full max-w-none justify-between"
              type="submit"
              disabled={isLoading || !hasSelection}
            >
              <span>CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default CheckboxListQuestion;
