import type React from 'react';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';

import { ComponentVariantRenderer } from './ComponentVariantRenderer';

/**
 * Higher-Order Component that enables A/B testing for onboarding pages.
 *
 * @param defaultComponent - The default component implementation
 * @param variants - Map of variant names to their component implementations
 * @returns A component that automatically selects the right variant based on backend response
 *
 * @example
 * ```typescript
 * const DefaultInfo = () => <div>Default version</div>;
 * const InfoVariantA = () => <div>Variant A version</div>;
 *
 * export default withComponentVariants(
 *   DefaultInfo,
 *   {
 *     'info-variant-a': InfoVariantA,
 *   }
 * );
 * ```
 */
export function withComponentVariants(
  defaultComponent: React.ComponentType,
  variants: Record<string, React.ComponentType>,
): React.ComponentType {
  const VariantEnabledPage = () => {
    const { onboarding } = useOnboardingNavigation();

    return (
      <ComponentVariantRenderer
        variants={variants}
        defaultComponent={defaultComponent}
        componentOverride={onboarding?.component}
      />
    );
  };

  // Set display name for easier debugging
  VariantEnabledPage.displayName = `withComponentVariants(${defaultComponent.displayName || defaultComponent.name || 'Component'})`;

  return VariantEnabledPage;
}
