import type React from 'react';

interface ComponentVariantRendererProps {
  variants: Record<string, React.ComponentType>;
  defaultComponent: React.ComponentType;
  componentOverride?: string | null;
}

/**
 * A reusable component that renders different component variants based on the componentOverride prop.
 * This enables A/B testing across onboarding pages.
 *
 * @param variants - Map of variant names to component implementations
 * @param defaultComponent - The default component to render when no override is specified
 * @param componentOverride - The variant name from the backend to override the default
 */
export function ComponentVariantRenderer({
  variants,
  defaultComponent,
  componentOverride,
}: ComponentVariantRendererProps) {
  // Select the component to render based on the override
  const ComponentToRender =
    componentOverride && variants[componentOverride]
      ? variants[componentOverride]
      : defaultComponent;

  return <ComponentToRender />;
}
