import type { OnboardingData } from '@/data/types';

import { camelToKebab } from './utils';

const PRE_SIGNUP_ROUTE_MAP: Record<string, string> = {
  stateSelection: '/account/state',
  firstAndLastName: '/account/name',
  createAccount: '/account/signup',
  unsupportedState: '/account/unsupported',
  unsupportedStateThankYou: '/account/thank-you',
};

export const resolvePreSignupRoute = (state: string | null | undefined) => {
  if (!state) return null;
  return PRE_SIGNUP_ROUTE_MAP[state] ?? null;
};

export const computeOnboardingRoute = (
  state: OnboardingData['state'],
): string => {
  if (!state) return '/onboarding/info';

  if (typeof state === 'object') {
    const preSignupState = state.preSignup;
    if (preSignupState) {
      const route = resolvePreSignupRoute(preSignupState);
      if (route) return route;
    }

    const questionnaireState = state.questionnaire;
    if (questionnaireState) {
      return `/onboarding/questionnaire/${camelToKebab(questionnaireState)}`;
    }

    // Special handling for processingPayment state - redirect to onboarded page
    if ('processingPayment' in state && state.processingPayment) {
      return '/onboarding/onboarded';
    }

    const fallbackState = Object.values(state).find(Boolean);
    if (fallbackState) {
      return `/onboarding/${camelToKebab(fallbackState)}`;
    }
  }

  if (typeof state === 'string') {
    if (state.startsWith('preSignup')) {
      const normalizedState = state.replace('preSignup.', '');
      const route = resolvePreSignupRoute(normalizedState);
      if (route) return route;
      return '/account/state';
    }

    if (state.startsWith('questionnaire.')) {
      const [, questionnaireState] = state.split('.');
      return questionnaireState
        ? `/onboarding/questionnaire/${camelToKebab(questionnaireState)}`
        : '/onboarding/questionnaire/age';
    }

    // Special handling for processingPayment state - redirect to onboarded page
    if (state === 'processingPayment') {
      return '/onboarding/onboarded';
    }

    return `/onboarding/${camelToKebab(state)}`;
  }

  return '/onboarding/info';
};

export const getQuestionnaireStateKey = (
  state: OnboardingData['state'],
): string | null => {
  if (!state) return null;
  if (typeof state === 'object' && state.questionnaire) {
    return state.questionnaire;
  }

  if (typeof state === 'string' && state.startsWith('questionnaire.')) {
    const [, questionnaireState] = state.split('.');
    return questionnaireState ?? null;
  }

  return null;
};

interface GetOnboardingFieldValueOptions {
  aliases?: string[];
}

const getNestedValue = (source: unknown, path: string) => {
  if (!source) return undefined;
  const segments = path.split('.');
  let current: unknown = source;
  for (const segment of segments) {
    if (
      current &&
      typeof current === 'object' &&
      segment in (current as Record<string, unknown>)
    ) {
      current = (current as Record<string, unknown>)[segment];
    } else {
      return undefined;
    }
  }
  return current;
};

export const getOnboardingFieldValue = <T>(
  snapshot: Pick<OnboardingData, 'value' | 'context'> | null | undefined,
  field: string,
  options: GetOnboardingFieldValueOptions = {},
) => {
  if (!snapshot) return undefined as T | undefined;

  const { aliases = [] } = options;
  const candidateKeys = [field, ...aliases];

  // Search in context (value is being phased out from backend)
  if (snapshot.context) {
    for (const key of candidateKeys) {
      // First try the key as-is (handles root-level fields and explicit dot notation)
      const value = getNestedValue(snapshot.context, key);
      if (value !== undefined) {
        return value as T;
      }

      // If not found and key doesn't contain dots, also try questionnaire nesting
      if (!key.includes('.')) {
        const questionnaireValue = getNestedValue(
          snapshot.context,
          `questionnaire.${key}`,
        );
        if (questionnaireValue !== undefined) {
          return questionnaireValue as T;
        }
      }
    }
  }

  return undefined as T | undefined;
};
