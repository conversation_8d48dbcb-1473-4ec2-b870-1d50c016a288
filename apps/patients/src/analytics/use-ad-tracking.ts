'use client';

import Cookies from 'js-cookie';

import type { CampaignData } from '@willow/utils/tracking';

export const getFbCookie = () => {
  return {
    fbp: Cookies.get('_fbp') ?? undefined,
    fbc: Cookies.get('_fbc') ?? undefined,
  };
};

export const getTiktokCookie = () => {
  return {
    ttclid: Cookies.get('ttclid') ?? undefined,
    ttp: Cookies.get('_ttp') ?? undefined,
  };
};

export const getGoogleCookie = () => {
  return {
    gclid: Cookies.get('_gclid') ?? undefined,
  };
};

export const getAffiliate = (): { type: 'impactRadius'; id: string } | null => {
  const id = Cookies.get('irclickid');
  if (!id) return null;
  return {
    type: 'impactRadius',
    id: id,
  };
};

export const getCampaignData = () => getCampaignDataFromCookies('l_');
export const getFirstTouchCampaignData = () =>
  getCampaignDataFromCookies('ft_');

const getCampaignDataFromCookies = (
  prefix: 'l_' | 'ft_',
): CampaignData | null => {
  const utm_source = Cookies.get(`${prefix}utm_source`);
  const utm_medium = Cookies.get(`${prefix}utm_medium`);
  const utm_campaign = Cookies.get(`${prefix}utm_campaign`);
  const utm_term = Cookies.get(`${prefix}utm_term`);
  const utm_content = Cookies.get(`${prefix}utm_content`);
  const utm_id = Cookies.get(`${prefix}utm_id`);

  if (
    utm_source ||
    utm_medium ||
    utm_campaign ||
    utm_term ||
    utm_content ||
    utm_id
  ) {
    return {
      source: utm_source ?? undefined,
      medium: utm_medium ?? undefined,
      name: utm_campaign ?? undefined,
      term: utm_term ?? undefined,
      content: utm_content ?? undefined,
    } as CampaignData;
  }
  return null;
};
