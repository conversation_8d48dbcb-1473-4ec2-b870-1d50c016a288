'use client';

import { useCallback } from 'react';
import mixpanel from 'mixpanel-browser';

import { env } from '~/env';
import { segment } from './segment';
import {
  getAffiliate,
  getCampaignData,
  getFbCookie,
  getFirstTouchCampaignData,
  getGoogleCookie,
  getTiktokCookie,
} from './use-ad-tracking';

export const useTracking = () => {
  const track = useCallback(
    (event: string, properties: Record<string, any> = {}) => {
      void segment.track(
        event,
        {
          firstCampaign: getFirstTouchCampaignData() ?? undefined,
          ...properties,
        },
        {
          context: {
            campaign: getCampaignData() ?? undefined,
            ...getFbCookie(),
            ...getTiktokCookie(),
            ...getGoogleCookie(),
          },
        },
      );
    },
    [],
  );

  const identify = useCallback(
    (userId: string, traits?: Record<string, any>) => {
      void segment.identify(userId, traits, {
        context: {
          campaign: getCampaignData() ?? undefined,
        },
      });
      if (env.NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN) {
        mixpanel.identify(userId);
      }
    },
    [],
  );

  const page = useCallback(
    (name?: string, properties: Record<string, any> = {}) => {
      void segment.page(
        name,
        {
          ...properties,
          firstCampaign: getFirstTouchCampaignData() ?? undefined,
        },
        {
          context: {
            campaign: getCampaignData() ?? undefined,
            referrer: getAffiliate() ?? undefined,
            ...getFbCookie(),
            ...getTiktokCookie(),
            ...getGoogleCookie(),
          },
        },
      );
    },
    [],
  );

  return { track, identify, page };
};
