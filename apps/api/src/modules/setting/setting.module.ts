import { Module } from '@nestjs/common';

import { AppCacheModule } from '../cache/cache.module';
import { PrismaModule } from '../prisma/prisma.module';
import { SettingController } from './setting.controller';
import { SettingService } from './setting.service';

@Module({
  imports: [PrismaModule, AppCacheModule],
  controllers: [SettingController],
  providers: [SettingService],
  exports: [SettingService],
})
export class SettingModule {}
