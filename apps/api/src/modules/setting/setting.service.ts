import type { Prisma, Setting, SettingStatus } from '@prisma/client';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import type { UpdateSettingDto } from './dto/update-setting.dto';
import { CacheService } from '../cache/cache.service';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class SettingService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
  ) {}

  async findAll(
    page = 1,
    limit = 10,
    search?: string,
    sortBy = 'createdAt',
    direction: 'asc' | 'desc' = 'desc',
    status?: string,
  ) {
    const skip = (page - 1) * limit;
    const take = +limit;

    // Never include archived settings in lists
    const where: Prisma.SettingWhereInput = {
      AND: [
        { NOT: { status: 'archived' } },
        status && status !== 'archived' ? ({ status } as any) : {},
      ],
      OR: search
        ? [
            { key: { contains: search, mode: 'insensitive' } },
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
          ]
        : undefined,
    };

    const [settings, total] = await Promise.all([
      this.prisma.setting.findMany({
        where,
        skip,
        take,
        orderBy: {
          [sortBy]: direction,
        },
      }),
      this.prisma.setting.count({ where }),
    ]);

    return {
      data: settings,
      meta: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string): Promise<Setting> {
    const setting = await this.prisma.setting.findUnique({
      where: { id },
    });

    // Treat archived settings as not found (conceptual soft-delete)
    if (!setting || setting.status === 'archived') {
      throw new NotFoundException(`Setting with ID ${id} not found`);
    }

    return setting;
  }

  async update(
    id: string,
    updateSettingDto: UpdateSettingDto,
  ): Promise<Setting> {
    const existingSetting = await this.findOne(id);

    if (existingSetting.status === 'archived') {
      throw new BadRequestException('Archived settings cannot be edited');
    }
    let data: Prisma.SettingUpdateInput;

    if (existingSetting.required) {
      // For required settings, only allow name, description, and value to change.
      const { name, description, value } = updateSettingDto;
      data = {
        ...(name !== undefined ? { name } : {}),
        ...(description !== undefined ? { description } : {}),
        ...(value !== undefined ? { value } : {}),
        // Explicitly ignore status, required, startDate, endDate
      };
    } else {
      // Non-required settings can update all standard fields
      const { startDate, endDate, ...rest } = updateSettingDto;
      data = {
        ...rest,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
      } as Prisma.SettingUpdateInput;
    }

    const setting = await this.prisma.setting.update({
      where: { id },
      data,
    });

    await this.invalidateCache(setting.key);

    // Set the .default cache entry with the selected value
    if (setting.value !== undefined && setting.value !== null) {
      await this.cacheService.setFlexible(
        `${setting.key}.default`,
        setting.value,
        1200, // 20 minutes (same as stale time in fetchByKey)
      );
    }

    return setting;
  }

  async remove(id: string): Promise<Setting> {
    const existingSetting = await this.findOne(id);

    if (existingSetting.required) {
      throw new BadRequestException('Required settings cannot be deleted');
    }

    const setting = await this.prisma.setting.update({
      where: { id },
      data: { status: 'archived' },
    });

    await this.invalidateCache(setting.key);
    return setting;
  }

  async fetchByKey(
    key: string,
    status: SettingStatus = 'active',
  ): Promise<Setting | null> {
    const cacheKey = `setting:${key}:${status}`;

    return this.cacheService.flexible<Setting | null>(
      cacheKey,
      [600, 1200], // 10 minutes fresh, 20 minutes stale
      async () => {
        return await this.prisma.setting.findFirst({
          where: {
            key,
            status,
          },
        });
      },
    );
  }

  /**
   * Fetch just the value of a setting using the .default cache pattern
   * This is more efficient than fetchByKey when you only need the value
   */
  async fetchDefaultValue(key: string): Promise<any | null> {
    return this.cacheService.flexible<any | null>(
      `${key}.default`,
      [600, 1200], // 10 minutes fresh, 20 minutes stale
      async () => {
        const setting = await this.prisma.setting.findFirst({
          where: { key, status: 'active' },
        });
        return setting?.value ?? null;
      },
    );
  }

  private async invalidateCache(key: string): Promise<void> {
    try {
      await Promise.all([
        this.cacheService.del(`setting:${key}:active`),
        this.cacheService.del(`setting:${key}:inactive`),
        this.cacheService.del(`setting:${key}:archived`),
        this.cacheService.del(`${key}.default`),
      ]);
    } catch (error) {
      console.warn('Failed to invalidate cache for setting:', key, error);
    }
  }
}
