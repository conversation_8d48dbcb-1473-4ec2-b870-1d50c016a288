import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

import { Capability } from '@willow/auth';

import { RequireCapabilities } from '../auth/decorators/require-capabilities.decorator';
import { CapabilityGuard } from '../auth/guards/capability.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { roles } from '../auth/types/roles';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { SettingService } from './setting.service';

@Controller('setting')
@UseGuards(AuthGuard('jwt'), RolesGuard, CapabilityGuard)
@Roles(roles.Admin)
@RequireCapabilities(Capability.MANAGE_SETTINGS)
export class SettingController {
  constructor(private readonly settingService: SettingService) {}

  @Get()
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('direction') direction?: 'asc' | 'desc',
    @Query('status') status?: string,
  ) {
    return this.settingService.findAll(
      page,
      limit,
      search,
      sortBy,
      direction,
      status,
    );
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.settingService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateSettingDto: UpdateSettingDto) {
    return this.settingService.update(id, updateSettingDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.settingService.remove(id);
  }
}
