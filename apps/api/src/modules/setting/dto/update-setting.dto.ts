import type { SettingStatus } from '@prisma/client';
import {
  IsBoolean,
  IsDateString,
  IsEnum,
  IsOptional,
  IsString,
} from 'class-validator';

export class UpdateSettingDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsOptional()
  value?: any;

  @IsOptional()
  valueOptions?: any; // Contains type info and options for select/multi-select

  @IsEnum(['active', 'inactive', 'archived'])
  @IsOptional()
  status?: SettingStatus;

  @IsBoolean()
  @IsOptional()
  required?: boolean;

  @IsDateString()
  @IsOptional()
  startDate?: string;

  @IsDateString()
  @IsOptional()
  endDate?: string;
}
