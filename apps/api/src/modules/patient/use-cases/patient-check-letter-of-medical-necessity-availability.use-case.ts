import { PrismaService } from '@/modules/prisma/prisma.service';
import { StripeService } from '@/modules/stripe/service/stripe.service';
import { TreatmentMachineContext } from '@modules/treatment/states/treatment.state';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CheckLetterOfMedicalNecessityAvailabilityUseCase {
  constructor(
    private readonly prisma: PrismaService,
    private readonly stripeService: StripeService,
  ) {}

  async execute(
    patientId: string,
  ): Promise<{ available: boolean; reason?: string }> {
    try {
      const patient = await this.prisma.patient.findFirst({
        where: { id: patientId },
        include: {
          user: true,
          shippingAddresses: {
            where: { default: true },
            include: { state: true },
          },
        },
      });
      if (!patient) {
        return { available: false, reason: 'Patient not found' };
      }
      if (!patient.doctorId) {
        return {
          available: false,
          reason:
            'You must be accepted by a doctor and have a core treatment to create the letter of medical necessity',
        };
      }
      const doctor = await this.prisma.doctor.findFirst({
        where: { id: patient.doctorId },
        include: {
          user: true,
          prescribesIn: { where: { stateId: patient.stateId } },
        },
      });
      if (!doctor) {
        return { available: false, reason: 'Doctor not found' };
      }
      const lastTreatment = await this.prisma.treatment.findFirst({
        where: {
          patientId: patient.id,
          isCore: true,
        },
        orderBy: { createdAt: 'desc' },
      });
      if (!lastTreatment) {
        return {
          available: false,
          reason:
            'No core treatment found, the letter of medical necessity can only be created for core treatments like Semaglutide or Tirzepatide',
        };
      }
      const treatmentStateContext: TreatmentMachineContext = (
        lastTreatment.state as any
      ).context;
      if (
        !treatmentStateContext.products ||
        treatmentStateContext.products.length === 0
      ) {
        return {
          available: false,
          reason: 'No products found in treatment, contact patient support',
        };
      }
      const firstProductPrice = await this.prisma.productPrice.findFirst({
        where: { id: treatmentStateContext.products[0].id },
        include: { product: true },
      });
      const activeProductPrice =
        treatmentStateContext.activeProduct?.id ==
        treatmentStateContext.products[0].id
          ? firstProductPrice
          : await this.prisma.productPrice.findFirst({
              where: { id: treatmentStateContext.activeProduct?.id },
              include: { product: true },
            });
      if (!activeProductPrice) {
        return {
          available: false,
          reason:
            'Unable to get the Letter of Medical Necessity, contact patient support',
        };
      }
      if (!firstProductPrice.compoundName || !activeProductPrice.compoundName) {
        return {
          available: false,
          reason:
            'Unable to get the Letter of Medical Necessity, contact patient support',
        };
      }
      if (
        !firstProductPrice.patientDirections ||
        !activeProductPrice.patientDirections
      ) {
        return {
          available: false,
          reason:
            'Unable to get the Letter of Medical Necessity, contact patient support',
        };
      }
      const lastPrescription = await this.prisma.prescription.findFirst({
        where: {
          treatmentId: lastTreatment.id,
          productPriceId: activeProductPrice.id,
        },
        orderBy: { createdAt: 'desc' },
      });
      if (
        !lastPrescription ||
        !lastPrescription.stripeInvoiceId ||
        lastTreatment.status === 'inProgress.waitingForPrescription'
      ) {
        return {
          available: false,
          reason:
            'The treatment is not ready yet, please wait for the prescription to be created and paid',
        };
      }
      // Optionally, check Stripe invoice exists
      try {
        await this.stripeService
          .client()
          .invoices.retrieve(lastPrescription.stripeInvoiceId);
      } catch {
        return {
          available: false,
          reason:
            'Unable to get the treatment invoice, contact patient support',
        };
      }
      return { available: true };
    } catch (e) {
      return { available: false, reason: e.message };
    }
  }
}
