import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { format, parse } from 'date-fns';

export interface VouchedVerificationRequest {
  lastFourSSN: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: string;
}

export interface VouchedVerificationResponse {
  ssnMatch: boolean;
  verificationId?: string;
}

@Injectable()
export class VouchedService {
  private readonly logger = new Logger(VouchedService.name);
  private readonly apiKey: string;
  private readonly apiUrl: string;
  private readonly isProduction: boolean;

  constructor(private readonly configService: ConfigService) {
    this.apiKey = this.configService.getOrThrow<string>('VOUCHED_API_KEY');
    this.apiUrl = this.configService.getOrThrow<string>('VOUCHED_API_URL');
    this.isProduction =
      this.configService.get<string>('ENVIRONMENT') === 'production';
  }

  async verifyIdentity(
    data: VouchedVerificationRequest,
  ): Promise<VouchedVerificationResponse> {
    try {
      const phone = data.phone ? data.phone.replace(/[^0-9]/g, '') : undefined;
      const dob = data.dateOfBirth
        ? format(
            parse(data.dateOfBirth, 'MM/dd/yyyy', new Date()),
            'yyyy-MM-dd',
          )
        : undefined;
      const payload = {
        firstName: data.firstName,
        lastName: data.lastName,
        phone,
        dob,
        ssn: data.lastFourSSN,
      };

      // In non-production environments, pass verification if SSN is '9999'
      if (!this.isProduction && data.lastFourSSN === '9999') {
        return {
          ssnMatch: true,
          verificationId: '1234567890',
        };
      }

      const response = await axios.post(this.apiUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          'X-Api-Key': this.apiKey,
        },
      });

      // Extract SSN match result from the response
      const ssnMatch = response.data.result?.ssnMatch === true;
      const verificationId = response.data.id;

      return {
        ssnMatch,
        verificationId,
      };
    } catch (error) {
      this.logger.error(`Vouched API error: ${error.message}`, error.stack);

      // Any error means SSN verification failed
      return {
        ssnMatch: false,
        verificationId: undefined,
      };
    }
  }
}
