import { PrismaTransactionalClient } from '@/modules/prisma/prisma.service';
import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import * as PgBoss from 'pg-boss';

// a simple function queue to ensure sequential execution of async functions
function createFnPromiseQueue() {
  let currentPromise = Promise.resolve();

  return function queueExecution(asyncFn: () => Promise<void>) {
    const promise = currentPromise.then(asyncFn);
    currentPromise = promise.catch(() => {}); // Continue queue even if one fails
    return promise;
  };
}

const createPgTaskQueue = createFnPromiseQueue();

@Injectable()
export default class PgBossClient implements OnModuleInit, OnModuleDestroy {
  public pgboss: PgBoss;

  constructor() {
    // private readonly options: QueueModuleOptions, // @Inject(QUEUE_MODULE_OPTIONS)
    this.pgboss = new PgBoss({
      connectionString: process.env.DATABASE_URL!,
      application_name: 'willow-api',
      schema: 'pgboss',
      deleteAfterDays: 60,
      archiveFailedAfterSeconds: 60 * 60 * 24 * 7, // 7 days
    });
    void this.connect();
  }

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.stop();
  }

  private async connect() {
    this.pgboss.on('error', (error) =>
      console.error('[PgBoss] error:', error.message),
    );
    await this.pgboss.start();
  }

  private async stop() {
    await this.pgboss.stop();
  }

  public mapPrismaToPgBoss(prisma: PrismaTransactionalClient): PgBoss.Db {
    return {
      async executeSql(sql: string, values: unknown[]) {
        const result = await prisma.$queryRawUnsafe<{ id: string }[]>(
          sql,
          ...values,
        );
        return { rows: result };
      },
    };
  }

  async isAlive(): Promise<boolean> {
    try {
      await this.pgboss.getQueues();
      return Promise.resolve(true);
    } catch (error) {
      return Promise.resolve(false);
    }
  }

  async ensureQueue(queueName: string, options?: PgBoss.Queue): Promise<void> {
    // Ensure queue creation and update are executed sequential to not deadlock table
    return createPgTaskQueue(async () => {
      await this.pgboss.createQueue(queueName, options);
      await this.pgboss.updateQueue(queueName, options);
    });
  }
}
