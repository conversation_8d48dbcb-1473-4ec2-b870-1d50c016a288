import { ONBOARDING_MACHINE_REGISTRY, OnboardingVersion } from '../states';

export function getQuestionnaireQuestionsFromMachine(
  onboardingVersion: OnboardingVersion,
) {
  // Get machine from registry
  let machine = ONBOARDING_MACHINE_REGISTRY[onboardingVersion];

  // For legacy versions without machines, fall back to v1 for question labels
  // (questionnaire structure is mostly consistent across versions)
  if (!machine) {
    machine = ONBOARDING_MACHINE_REGISTRY['v1'];

    // If v1 also doesn't exist (shouldn't happen), throw error
    if (!machine) {
      throw new Error(
        `Unable to extract questions: no machine found for ${onboardingVersion} and v1 fallback is unavailable`,
      );
    }
  }

  // Extract questions from the machine's questionnaire states
  const questions: Record<string, string> = {};
  const questionnaireStates = machine.config.states?.questionnaire?.states;

  if (questionnaireStates) {
    Object.entries(questionnaireStates).forEach(([stateName, stateConfig]) => {
      const meta = (stateConfig as any).meta;
      if (meta?.question) {
        // Map state names to their corresponding questionnaire keys
        const questionKey = mapStateNameToQuestionKey(stateName);
        if (questionKey) {
          questions[questionKey] = meta.question;
        }
      }
    });
  }

  return questions;
}

function mapStateNameToQuestionKey(stateName: string): string | null {
  const mapping: Record<string, string> = {
    age: 'birthDate',
    gender: 'gender',
    isPregnant: 'isPregnant',
    usingGLP1: 'usingGLP1',
    haveDiabetes: 'haveDiabetes',
    doctorVisits: 'doctorVisits',
    qualifyingConditions: 'qualifyingConditions',
    height: 'height',
    weight: 'weight',
    desiredWeight: 'desiredWeight',
    objectives: 'objectives',
    haveAllergies: 'hasAllergies',
    selectAllergies: 'allergies',
    medications: 'medications',
    medicalConditions: 'medicalConditions',
    additionalInformation: 'additionalInformation',
  };

  return mapping[stateName] || null;
}
