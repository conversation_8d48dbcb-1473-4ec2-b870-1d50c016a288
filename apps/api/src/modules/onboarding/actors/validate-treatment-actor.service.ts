import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { fromPromise } from 'xstate';
import { z } from 'zod';

import { BaseActorService } from './base-actor.service';

@Injectable()
export class ValidateTreatmentActorService extends BaseActorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly auditService: AuditService,
  ) {
    super();
  }

  createActor() {
    return fromPromise<
      {
        success: boolean;
        products: Array<{
          id: string;
          label: string;
          image: string;
          price: number;
          priceId: string;
          type: 'core' | 'additional';
          supplyLength: string;
          form: string;
          dosageLabel: string;
          description?: string;
        }>;
      },
      { context?: any; products?: string | string[]; vials?: number }
    >(async ({ input }) => {
      try {
        const schema = z.object({
          products: z.union([
            z.string().min(1, 'Product ID is required'),
            z
              .array(z.string().min(1, 'Product ID is required'))
              .min(0)
              .max(2, 'Maximum 2 products allowed'),
          ]),
        });
        const validated = schema.parse({ products: input.products });

        const productIds = Array.isArray(validated.products)
          ? validated.products
          : [validated.products];

        const products = await this.prisma.product.findMany({
          where: {
            id: { in: productIds },
          },
          select: {
            id: true,
            name: true,
            label: true,
            onboardingLabel: true,
            image: true,
            description: true,
            type: true,
            form: true,
            supplyLength: true,
            defaultPrice: {
              select: {
                id: true,
                unit_amount: true,
                dosageLabel: true,
              },
            },
          },
        });

        if (products.length !== productIds.length) {
          throw new Error('One or more products not found');
        }

        // Transform products to match the context type
        const transformedProducts = products.map((product) => ({
          id: product.id,
          label: product.onboardingLabel,
          image: product.image,
          price: product.defaultPrice?.unit_amount
            ? product.defaultPrice.unit_amount / 100
            : 0,
          priceId: product.defaultPrice?.id || '',
          type: product.type as 'core' | 'additional',
          supplyLength: product.supplyLength,
          form: product.form,
          dosageLabel: product.defaultPrice?.dosageLabel || '',
          description: product.description || undefined,
        }));

        // Log audit event for product selection
        const patientId = input.context?.patientId;
        if (patientId) {
          // Create audit log format with name and vials
          const auditProducts = transformedProducts.map((product) => ({
            id: product.id,
            form: product.form,
            name: products.find((p) => p.id === product.id)?.name || '',
            type: product.type,
            image: product.image,
            label: product.label,
            price: product.price,
            vials: product.type === 'core' ? (input.vials ?? 1) : 1,
            description: product.description,
            dosageLabel: product.dosageLabel,
          }));

          void this.auditService.append({
            patientId,
            action: 'ONBOARDING_TREATMENT_SELECTED',
            actorType: 'PATIENT',
            actorId: patientId,
            resourceType: 'PATIENT',
            resourceId: patientId,
            details: {
              selectedTreatment: auditProducts,
            },
          });
        }

        return {
          success: true,
          products: transformedProducts,
        };
      } catch (error) {
        if (error instanceof z.ZodError) {
          this.handleZodError(error, 'Treatment validation failed');
        }
        throw error;
      }
    });
  }
}
