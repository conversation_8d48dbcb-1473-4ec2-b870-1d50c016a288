import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { AuditService } from '@modules/audit-log/audit-log.service';
import { OnboardingSnapshot } from '@modules/onboarding/services/onboarding-state.service';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { PharmacyService } from '@modules/pharmacy/pharmacy.service';
import { Injectable } from '@nestjs/common';
import { fromPromise } from 'xstate';
import { z } from 'zod';

import type { OnboardingCookieService } from '../services/onboarding-cookie.service';
import { BaseActorService } from './base-actor.service';

@Injectable()
export class ValidateTreatmentTypeActorService extends BaseActorService {
  constructor(
    private readonly patientPersistence: PatientPersistence,
    private readonly auditService: AuditService,
    private readonly pharmacyService: PharmacyService,
    private readonly prismaService: PrismaService,
    private readonly segment: SegmentService,
  ) {
    super();
  }

  createActor(cookieService: OnboardingCookieService, cookie?: any) {
    return fromPromise<
      { treatmentType: string; pharmacyId: string },
      { treatmentType?: string }
    >(async ({ input }) => {
      try {
        const schema = z.object({
          treatmentType: z.string().min(1, 'Treatment type is required'),
        });
        const validated = schema.parse(input);

        const runtimeCookie = cookieService.tryGetCookie() ?? cookie;
        const patientId = runtimeCookie?.authTokens?.patientId;
        const snapshot = (runtimeCookie as any)?.stateSnapshot as
          | AnyOnboardingSnapshot
          | undefined;

        if (!patientId) {
          throw new Error('Patient ID is required');
        }

        const categoryId = validated.treatmentType;

        if (!categoryId) {
          throw new Error('Category ID is required');
        }

        // Fetch the product category to get its form
        const productCategory =
          await this.prismaService.productCategory.findFirstOrThrow({
            where: { id: categoryId, enabled: true },
            select: { id: true, form: true, name: true, enabled: true },
          });

        const profile: PatientOnboardingProfile =
          await this.patientPersistence.getOnboardingProfile(patientId);

        const resolvedPharmacy =
          await this.pharmacyService.resolvePharmacyPriority(
            patientId,
            profile.stateId,
            categoryId,
            snapshot as OnboardingSnapshot,
          );

        if (resolvedPharmacy && profile.pharmacyId !== resolvedPharmacy.id) {
          // Update the patient's pharmacy if it has changed
          await this.prismaService.patient.update({
            where: { id: patientId },
            data: { pharmacyId: resolvedPharmacy.id },
          });
        }

        // Use the form from the product category for audit log
        void this.auditService.append({
          patientId,
          action: 'ONBOARDING_TREATMENT_FORM_SELECTED',
          actorType: 'PATIENT',
          actorId: patientId,
          resourceType: 'PATIENT',
          resourceId: patientId,
          details: {
            selectedForm:
              (productCategory.form as 'oral' | 'injectable' | 'tablet') ||
              'injectable',
          },
        });

        void this.segment.track(patientId, 'treatmentTypeChosen', {
          properties: {
            type: productCategory.form,
            categoryId,
            categoryName: productCategory.name,
          },
        });

        return {
          treatmentType: input.treatmentType,
          pharmacyId: resolvedPharmacy?.id ?? profile.pharmacyId,
        };
      } catch (error) {
        if (error instanceof z.ZodError) {
          this.handleZodError(error, 'Treatment type validation failed');
        }
        throw error;
      }
    });
  }
}
