import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { fromPromise } from 'xstate';
import { z } from 'zod';

import { BaseActorService } from './base-actor.service';

@Injectable()
export class CheckStateEnabledActorService extends BaseActorService {
  constructor(private readonly prisma: PrismaService) {
    super();
  }

  createActor() {
    return fromPromise<{ state: string; enabled: boolean }, { state?: string }>(
      async ({ input }) => {
        try {
          const schema = z.object({
            state: z.string().min(2).max(2, 'State code must be 2 characters'),
          });
          const validated = schema.parse(input);

          const state = await this.prisma.state.findFirstOrThrow({
            where: { code: validated.state },
            select: { code: true, enabled: true },
          });

          return {
            state: state.code,
            enabled: state.enabled,
          };
        } catch (error) {
          if (error instanceof z.ZodError) {
            this.handleZodError(error, 'State validation failed');
          }
          throw error;
        }
      },
    );
  }
}
