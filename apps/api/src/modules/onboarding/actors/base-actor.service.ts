import { z } from 'zod';

import type { ValidationErrorField } from '../errors/onboarding-validation.error';
import { OnboardingValidationError } from '../errors/onboarding-validation.error';

export abstract class BaseActorService {
  protected throwValidationError(
    message: string,
    errors?: ValidationErrorField[],
  ): never {
    throw new OnboardingValidationError(message, errors ?? []);
  }

  protected handleZodError(error: z.ZodError, message: string): never {
    const fieldErrors: ValidationErrorField[] = error.errors.map((err) => ({
      field: err.path.join('.'),
      message: err.message,
    }));

    this.throwValidationError(message, fieldErrors);
  }
}
