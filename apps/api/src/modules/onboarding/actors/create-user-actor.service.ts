import type { OnboardingVersion } from '@modules/onboarding/states';
import { PatientService } from '@/modules/patient/patient.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { states } from '@modules/shared/constants/states';
import { Injectable } from '@nestjs/common';
import * as tlds from 'tlds';
import { fromPromise } from 'xstate';
import { z } from 'zod';

import { BaseActorService } from './base-actor.service';

// Special characters regex from PatientSignUpDto
const specialCharsRegex = /^[a-zA-Z0-9!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~\s]*$/;

// Phone number format: (XXX) XXX-XXXX
const PHONE_FORMAT = /^\(\d{3}\)\s\d{3}-\d{4}$/;

// Regex to check for 7 or more repeating digits
const REPEATING_DIGITS = /(\d)\1{6,}/;

// List of valid US area codes
const VALID_AREA_CODES = [
  201, 202, 203, 205, 206, 207, 208, 209, 210, 212, 213, 214, 215, 216, 217,
  218, 219, 220, 223, 224, 225, 227, 228, 229, 231, 234, 235, 239, 240, 248,
  251, 252, 253, 254, 256, 260, 262, 267, 269, 270, 272, 274, 276, 279, 281,
  283, 301, 302, 303, 304, 305, 307, 308, 309, 310, 312, 313, 314, 315, 316,
  317, 318, 319, 320, 321, 323, 324, 325, 326, 327, 329, 330, 331, 332, 334,
  336, 337, 339, 341, 346, 347, 350, 351, 352, 353, 360, 361, 363, 364, 369,
  380, 385, 386, 401, 402, 404, 405, 406, 407, 408, 409, 410, 412, 413, 414,
  415, 417, 419, 423, 424, 425, 430, 432, 434, 435, 436, 440, 442, 443, 445,
  447, 448, 458, 463, 464, 469, 470, 472, 475, 478, 479, 480, 484, 501, 502,
  503, 504, 505, 507, 508, 509, 510, 512, 513, 515, 516, 517, 518, 520, 530,
  531, 534, 539, 540, 541, 551, 557, 559, 561, 562, 563, 564, 567, 570, 571,
  572, 573, 574, 575, 580, 582, 585, 586, 601, 602, 603, 605, 606, 607, 608,
  609, 610, 612, 614, 615, 616, 617, 618, 619, 620, 623, 624, 626, 628, 629,
  630, 631, 636, 640, 641, 645, 646, 650, 651, 656, 657, 659, 660, 661, 662,
  667, 669, 678, 680, 681, 682, 686, 689, 701, 702, 703, 704, 706, 707, 708,
  712, 713, 714, 715, 716, 717, 718, 719, 720, 724, 725, 726, 727, 728, 730,
  731, 732, 734, 737, 740, 743, 747, 754, 757, 760, 762, 763, 765, 769, 770,
  771, 772, 773, 774, 775, 779, 781, 785, 786, 801, 802, 803, 804, 805, 806,
  808, 810, 812, 813, 814, 815, 816, 817, 818, 820, 826, 828, 830, 831, 832,
  835, 838, 839, 840, 843, 845, 847, 848, 850, 854, 856, 857, 858, 859, 860,
  861, 862, 863, 864, 865, 870, 872, 878, 901, 903, 904, 906, 907, 908, 909,
  910, 912, 913, 914, 915, 916, 917, 918, 919, 920, 925, 928, 929, 930, 931,
  934, 936, 937, 938, 940, 941, 943, 945, 947, 948, 949, 951, 952, 954, 956,
  959, 970, 971, 972, 973, 975, 978, 979, 980, 983, 984, 985, 986, 989,
];

@Injectable()
export class CreateUserActorService extends BaseActorService {
  constructor(
    private readonly patientService: PatientService,
    private readonly prisma: PrismaService,
  ) {
    super();
  }

  createActor(cookie?: any) {
    return fromPromise<{ success: boolean; authData?: any }, any>(
      async ({ input }) => {
        try {
          const schema = z.object({
            firstName: z
              .string()
              .min(1, 'First name is required')
              .max(35, 'First name must not exceed 35 characters')
              .regex(
                specialCharsRegex,
                'First name contains invalid characters',
              ),
            lastName: z
              .string()
              .min(1, 'Last name is required')
              .max(35, 'Last name must not exceed 35 characters')
              .regex(
                specialCharsRegex,
                'Last name contains invalid characters',
              ),
            email: z
              .string()
              .min(1, 'Email is required')
              .email('Invalid email format')
              .toLowerCase()
              .refine(
                (email) => {
                  const topLevelDomain = email
                    .split('@')
                    .pop()
                    ?.split('.')
                    .pop();
                  return topLevelDomain && tlds.includes(topLevelDomain);
                },
                { message: 'Please enter a valid email address' },
              ),
            password: z
              .string()
              .min(1, 'Password is required')
              .min(8, 'Password must be at least 8 characters'),
            phone: z
              .string()
              .min(1, 'Phone is required')
              .max(25, 'Phone number must not exceed 25 characters')
              .refine((phone) => PHONE_FORMAT.test(phone), {
                message:
                  'Please enter a valid US phone number in the format (XXX) XXX-XXXX',
              })
              .refine(
                (phone) => {
                  const digits = phone.replace(/\D/g, '');
                  return !REPEATING_DIGITS.test(digits);
                },
                { message: 'Phone number contains too many repeating digits' },
              )
              .refine(
                (phone) => {
                  const areaCode = parseInt(phone.substring(1, 4));
                  return VALID_AREA_CODES.includes(areaCode);
                },
                {
                  message:
                    'Please enter a valid US phone number in the format (XXX) XXX-XXXX',
                },
              ),
            state: z
              .string()
              .min(1, 'State is required')
              .refine((state) => states.includes(state), {
                message: 'Invalid state',
              }),
            getPromotionsSMS: z.boolean().optional(),
            discount: z
              .object({
                type: z.enum(['referral', 'coupon']),
                code: z.string(),
                label: z.string(),
                value: z.number(),
                valueType: z.enum(['percentage', 'fixed']),
              })
              .optional(),
          });

          const validated = schema.parse(input);

          // Check if user with this email already exists
          const existingUser = await this.prisma.user.findFirst({
            where: { email: validated.email, type: 'patient', deletedAt: null },
          });

          if (existingUser) {
            return {
              success: true,
              authData: {
                requiresAutoLogin: true,
                email: validated.email,
                password: validated.password,
                patientId: existingUser.id,
              },
            };
          }

          const onboardingVersion = cookie?.version as OnboardingVersion;
          const onboardingState = (cookie as any)?.stateSnapshot || {};

          // Split discount into promoCoupon and referralCode
          const promoCoupon =
            validated.discount?.type === 'coupon'
              ? validated.discount.code
              : undefined;
          const referralCode =
            validated.discount?.type === 'referral'
              ? validated.discount.code
              : undefined;

          const authData = await this.patientService.signUp(
            {
              email: validated.email,
              password: validated.password,
              firstName: input.firstName,
              lastName: input.lastName,
              phone: validated.phone,
              state: input.state,
              getPromotionsSMS: validated.getPromotionsSMS,
              promoCoupon,
            },
            onboardingState,
            onboardingVersion,
            referralCode,
          );

          return { success: true, authData, onboardingVersion };
        } catch (error) {
          if (error instanceof z.ZodError) {
            this.handleZodError(error, 'Account creation validation failed');
          }
          throw error;
        }
      },
    );
  }
}
