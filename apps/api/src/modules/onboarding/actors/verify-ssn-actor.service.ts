import { VouchedService } from '@/modules/shared/services/vouched.service';
import { Injectable } from '@nestjs/common';
import { fromPromise } from 'xstate';
import { z } from 'zod';

import { BaseActorService } from './base-actor.service';

@Injectable()
export class VerifySsnActorService extends BaseActorService {
  constructor(private readonly vouchedService: VouchedService) {
    super();
  }

  createActor() {
    return fromPromise<
      { success: boolean; verificationId: string },
      {
        lastFourSSN: string;
        firstName: string;
        lastName: string;
        phone: string;
        dateOfBirth: string;
        patientId: string;
      }
    >(async ({ input }) => {
      try {
        const schema = z.object({
          lastFourSSN: z.string().regex(/^[0-9]{4}$/),
          firstName: z.string().optional(),
          lastName: z.string().optional(),
          phone: z.string().optional(),
          dateOfBirth: z.string().optional(),
        });
        schema.parse(input);

        const vouchedResponse = await this.vouchedService.verifyIdentity(input);

        if (!vouchedResponse.ssnMatch) {
          throw new Error('SSN verification failed');
        }

        return {
          success: true,
          verificationId: vouchedResponse.verificationId,
        };
      } catch (error) {
        if (error instanceof z.ZodError) {
          this.handleZodError(error, 'SSN validation failed');
        }
        throw error;
      }
    });
  }
}
