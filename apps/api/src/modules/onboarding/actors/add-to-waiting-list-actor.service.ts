import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import { fromPromise } from 'xstate';
import { z } from 'zod';

import { BaseActorService } from './base-actor.service';

@Injectable()
export class AddToWaitingListActorService extends BaseActorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly segment: SegmentService,
  ) {
    super();
  }

  createActor() {
    return fromPromise<
      { success: boolean },
      { email?: string; state?: string }
    >(async ({ input }) => {
      try {
        const schema = z.object({
          email: z.string().email('Invalid email format'),
          state: z.string(),
        });

        const validated = schema.parse(input);
        const { email, state } = validated;

        // Check if state exists and is disabled for onboarding
        const { id: stateId, enabled } =
          await this.prisma.state.findFirstOrThrow({
            where: { code: state },
            select: { id: true, enabled: true },
          });

        if (enabled) {
          throw new Error(`State ${state} is enabled for onboarding`);
        }

        // Generate ID for segment tracking
        const id = uuidv4();

        // Track segment event
        await this.segment.track(id, 'WaitlistJoined', {
          properties: {
            email,
            state,
          },
        });

        // Create waiting list entry
        try {
          await this.prisma.patientWaitingList.create({
            data: {
              email,
              stateId,
            },
          });
        } catch (error) {
          if (
            error instanceof Prisma.PrismaClientKnownRequestError &&
            error.code === 'P2002'
          ) {
            // Duplicate entry - silently return success
            return { success: true };
          }
          // Re-throw any other errors
          throw error;
        }

        return { success: true };
      } catch (error) {
        if (error instanceof z.ZodError) {
          this.handleZodError(error, 'Waiting list validation failed');
        }
        throw error;
      }
    });
  }
}
