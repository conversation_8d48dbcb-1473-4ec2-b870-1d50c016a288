import { Injectable } from '@nestjs/common';
import { fromPromise } from 'xstate';
import { z } from 'zod';

import { BaseActorService } from './base-actor.service';

@Injectable()
export class ValidateNameActorService extends BaseActorService {
  createActor() {
    return fromPromise<
      { valid: boolean; errors?: string[] },
      { firstName?: string; lastName?: string }
    >(async ({ input }) => {
      try {
        const specialCharsRegex =
          /^[a-zA-Z0-9!"#$%&'()*+,\-./:;<=>?@[\]\\^_`{|}~\s]*$/;
        const schema = z.object({
          firstName: z
            .string()
            .min(1, 'First name is required')
            .max(35, 'First name too long')
            .regex(specialCharsRegex, 'First name contains invalid characters'),
          lastName: z
            .string()
            .min(1, 'Last name is required')
            .max(35, 'Last name too long')
            .regex(specialCharsRegex, 'Last name contains invalid characters'),
        });

        schema.parse(input);
        return { valid: true };
      } catch (error) {
        if (error instanceof z.ZodError) {
          this.handleZodError(error, 'Name validation failed');
        }
        throw error;
      }
    });
  }
}

// Keep the old function for backwards compatibility during transition
export function createValidateNameActor() {
  const service = new ValidateNameActorService();
  return service.createActor();
}
