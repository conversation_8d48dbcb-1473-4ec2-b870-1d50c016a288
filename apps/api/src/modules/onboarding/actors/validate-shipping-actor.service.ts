import { PrismaService } from '@/modules/prisma/prisma.service';
import { TaxjarService } from '@/modules/shared/services/taxjar.service';
import { OnboardingValidationError } from '@modules/onboarding/errors/onboarding-validation.error';
import { AddressMismatchError } from '@modules/shared/errors/address-mismatch.error';
import { PoBoxError } from '@modules/shared/errors/po-box.error';
import { Injectable } from '@nestjs/common';
import { fromPromise } from 'xstate';
import { z } from 'zod';

import { BaseActorService } from './base-actor.service';

@Injectable()
export class ValidateShippingActorService extends BaseActorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly taxjarService: TaxjarService,
  ) {
    super();
  }

  createActor() {
    return fromPromise<{ shippingInfo?: any }, { shippingInfo?: any }>(
      async ({ input }) => {
        try {
          const specialCharsRegex =
            /^[a-zA-Z0-9!"#$%&'()*+,\-./:;<=>?@[\]\\^_`{|}~\s]*$/;
          const schema = z.object({
            address1: z
              .string()
              .min(1, 'Address 1 is required')
              .max(35, 'Address 1 too long')
              .regex(
                specialCharsRegex,
                'Address 1 contains invalid characters',
              ),
            address2: z
              .string()
              .max(35, 'Address 2 too long')
              .regex(specialCharsRegex, 'Address 2 contains invalid characters')
              .optional(),
            city: z
              .string()
              .min(1, 'City is required')
              .max(35, 'City too long')
              .regex(specialCharsRegex, 'City contains invalid characters'),
            state: z.string().min(1, 'State is required'),
            zip: z.string().min(1, 'ZIP code is required'),
            phone: z.string().max(25, 'Phone too long').optional(),
            force: z.boolean().optional(),
          });

          const validated = schema.parse(input.shippingInfo);

          const state = await this.prisma.state.findFirst({
            where: { code: validated.state },
            select: { code: true, enabled: true },
          });

          if (!state) {
            throw new Error('Invalid state');
          }

          if (!state.enabled) {
            throw new Error(
              'Willow does not support servicing this state. Please reach out to Patient Care Team for more information.',
            );
          }

          await this.taxjarService.validateAddress(validated as any);

          return { shippingInfo: validated };
          //
        } catch (error) {
          if (error instanceof z.ZodError) {
            this.handleZodError(error, 'Shipping validation failed');
          } else if (error instanceof PoBoxError) {
            this.throwValidationError(error.message);
          } else if (error instanceof AddressMismatchError) {
            console.log('error!!!');
          }
          throw new OnboardingValidationError(error.message);
        }
      },
    );
  }
}
