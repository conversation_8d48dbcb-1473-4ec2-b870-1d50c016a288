import { assign, emit, setup } from 'xstate';

import type {
  OnboardingContext,
  OnboardingEvents,
  OnboardingInput,
  OnboardingProduct,
} from './types/onboarding-generic.types';
import { OnboardingValidationError } from '../errors/onboarding-validation.error';
import { QUESTIONNAIRE_QUESTIONS } from '../utils/questionnaire-questions';
import * as actions from './actions';
import { actorSchemas } from './actor-schemas';
import * as guards from './guards';

const version = 'v3';

export const onboardingV3Machine = setup({
  types: {
    context: {} as OnboardingContext,
    events: {} as OnboardingEvents,
    input: {} as OnboardingInput,
  },
  actors: actorSchemas,
  guards: { ...guards },
  actions: {
    track: emit(({ context, event }, params) => ({
      type: 'track',
      data: { context, ...params, event },
    })),
    identify: emit(({ context, event }, params) => ({
      type: 'identify',
      data: { context, ...params, event },
    })),
    log: emit(({ context, event }, params) => ({
      type: 'audit',
      data: { context, ...params, event },
    })),
    reject: assign((_, params: { reason: string }) =>
      actions.getRejectPayload(params.reason),
    ),
    clearRejected: assign(() => actions.getClearRejectedPayload()),
    complete: assign(() => actions.getCompletePayload()),
    storeQuestionnaire: assign(({ context, event }, params) =>
      actions.mergeQuestionnaireData(context, event, params),
    ),
    store: assign(({ context, event }) =>
      actions.mergeStoreData(context, event),
    ),
    storeProductsFromActor: assign(
      (
        { context },
        params: {
          output: {
            success: boolean;
            products: OnboardingProduct[];
          };
        },
      ) => actions.storeProductsFromActor(context, params),
    ),
    storePreSignup: assign(({ context, event }) =>
      actions.mergePreSignupData(context, event),
    ),
    storeSsnData: assign(({ context, event }) =>
      actions.mergeSsnData(context, event),
    ),
    storeSsnSuccess: assign(
      (
        { context },
        params: { output: { success: boolean; verificationId?: string } },
      ) => actions.getSsnSuccessPayload(context, params.output),
    ),
    storeSsnFailure: assign(({ context }) =>
      actions.getSsnFailurePayload(context),
    ),
    clearSsnVerificationState: assign(({ context }) =>
      actions.getClearSsnVerificationPayload(context),
    ),
    calculateAndStoreBMI: assign(({ context, event }) =>
      actions.calculateAndStoreBMI(context, event),
    ),
    resetContext: assign(({ context }, params) =>
      actions.getResetContextPayload(context, params),
    ),
    attachDiscount: assign(({ context }, params) =>
      actions.attachDiscountPayload(context, params),
    ),
    clearValidationError: assign(() => ({
      validationError: undefined,
    })),
    assignValidationError: assign(({ event }) => {
      const error = (event as any).error;
      if (error instanceof OnboardingValidationError) {
        return error.toContext();
      }
      return {
        validationError: {
          type: 'SystemError' as const,
          message: 'An unexpected error occurred',
          errors: [],
        },
      };
    }),
  },
}).createMachine({
  context: ({ input }) => ({
    questionnaireCompleted: false,
    questionnaire: {},
    ...(input?.existingData || {}),
  }),
  id: `onboarding-${version}`,
  initial: 'preSignup',
  meta: {
    total: 3 + 15 + 12,
  },
  on: {
    reset: {
      target: '.questionnaire.age',
      actions: {
        type: 'resetContext',
        params: ({ event }) => ({
          data: event.data,
        }),
      },
    },
    clearValidationError: {
      actions: 'clearValidationError',
    },
  },
  states: {
    preSignup: {
      initial: 'stateSelection',
      states: {
        stateSelection: {
          on: {
            submit: {
              target: 'checkingState',
              actions: 'storePreSignup',
            },
          },
          meta: {
            step: 1,
            name: 'Account Creation',
            content: {},
          },
        },
        checkingState: {
          invoke: {
            src: 'checkStateEnabled',
            input: ({ event }) => ({
              state: (event as any).value?.state,
            }),
            onDone: [
              { target: 'firstAndLastName', guard: 'isValidState' },
              { target: 'unsupportedState' },
            ],
            onError: {
              target: 'stateSelection',
              actions: 'assignValidationError',
            },
          },
        },
        firstAndLastName: {
          on: {
            submit: {
              target: 'validatingName',
              actions: 'storePreSignup',
            },
            back: {
              target: 'stateSelection',
            },
          },
          meta: {
            step: 2,
            name: 'Account Creation',
            content: {},
          },
        },
        validatingName: {
          invoke: {
            src: 'validateName',
            input: ({ event }) => ({
              firstName: (event as any).value?.firstName,
              lastName: (event as any).value?.lastName,
            }),
            onDone: { target: 'createAccount' },
            onError: {
              target: 'firstAndLastName',
              actions: 'assignValidationError',
            },
          },
        },
        createAccount: {
          on: {
            submit: {
              target: 'creatingAccount',
              actions: 'storePreSignup',
            },
            back: {
              target: 'firstAndLastName',
            },
          },
          meta: {
            step: 3,
            name: 'Account Creation',
            content: {},
          },
        },
        creatingAccount: {
          invoke: {
            src: 'createUser',
            input: ({ event, context }) => ({
              ...(event as any).value,
              firstName: context.firstName,
              lastName: context.lastName,
              state: context.state,
              discount: context.discount,
            }),
            onDone: [
              {
                guard: 'requiresAutoLogin',
                target: 'createAccount',
                actions: assign(({ event }) => {
                  return {
                    authData: event.output.authData,
                    validationError: {
                      type: 'ValidationError' as const,
                      message: 'Email already exists',
                      errors: [
                        {
                          field: 'email',
                          message: 'Email already exists',
                        },
                      ],
                    },
                  };
                }),
              },
              {
                target: `#onboarding-${version}.questionnaire`,
                actions: [
                  assign(({ event }) => {
                    return {
                      authData: event.output.authData,
                      patientId: event.output.authData.patientId,
                    };
                  }),
                  {
                    type: 'log',
                    params: { action: 'PATIENT_ACCOUNT_CREATED' },
                  },
                  { type: 'track', params: { eventName: 'AccountCreated' } },
                  { type: 'track', params: { eventName: 'SignUp' } },
                  {
                    type: 'identify',
                    params: {
                      trait: 'accountCreated',
                      source: 'CreateUserActorService',
                      startAfterSeconds: 10,
                    },
                  },
                ],
              },
            ],
            onError: {
              target: 'createAccount',
              actions: 'assignValidationError',
            },
          },
        },
        unsupportedState: {
          on: {
            submit: {
              target: 'addingToWaitingList',
            },
            back: {
              target: 'stateSelection',
              actions: 'clearRejected',
            },
          },
          meta: {
            step: 2,
            name: 'Account Creation',
            content: {},
          },
        },
        addingToWaitingList: {
          invoke: {
            src: 'addToWaitingList',
            input: ({ context, event }) => ({
              email: (event as any).value?.email,
              state: context.state,
            }),
            onDone: {
              target: 'unsupportedStateThankYou',
            },
            onError: {
              target: 'unsupportedState',
              actions: 'assignValidationError',
            },
          },
        },
        unsupportedStateThankYou: {
          on: {
            back: {
              target: 'stateSelection',
            },
          },
          meta: {
            step: 3,
            name: 'Account Creation',
            content: {},
          },
        },
      },
    },
    questionnaire: {
      meta: { total: 15 },
      initial: 'age',
      states: {
        age: {
          exit: [
            {
              type: 'identify',
              params: { trait: 'age', source: 'questionnaire' },
            },
          ],
          on: {
            next: [
              {
                guard: 'isUnderAge',
                target: 'rejectedUnderAge',
                actions: 'storeQuestionnaire',
              },
              {
                guard: 'isOverAge',
                target: 'rejectedOverAge',
                actions: 'storeQuestionnaire',
              },
              { target: 'gender', actions: 'storeQuestionnaire' },
            ],
          },
          meta: {
            step: 5,
            question: QUESTIONNAIRE_QUESTIONS.birthDate,
            valueMap: {
              birthDate: 'questionnaire.birthDate',
            },
            content: {},
          },
        },
        rejectedUnderAge: {
          entry: [
            { type: 'reject', params: { reason: 'under age' } },
            { type: 'track', params: { eventName: 'OnboardingReject' } },
          ],
          on: { back: { target: 'age', actions: 'clearRejected' } },
          meta: {
            step: 5,
            content: {},
          },
        },
        rejectedOverAge: {
          entry: [
            { type: 'reject', params: { reason: 'over age' } },
            { type: 'track', params: { eventName: 'OnboardingReject' } },
          ],
          on: { back: { target: 'age', actions: 'clearRejected' } },
          meta: {
            step: 5,
            content: {},
          },
        },
        gender: {
          exit: [
            {
              type: 'identify',
              params: { trait: 'gender', source: 'questionnaire' },
            },
          ],
          on: {
            back: { target: 'age' },
            male: {
              target: 'usingGLP1',
              actions: {
                type: 'storeQuestionnaire',
                params: { gender: 'male' },
              },
            },
            female: {
              target: 'isPregnant',
              actions: {
                type: 'storeQuestionnaire',
                params: { gender: 'female' },
              },
            },
          },
          meta: {
            step: 7,
            question: QUESTIONNAIRE_QUESTIONS.gender,
            content: {},
          },
        },
        isPregnant: {
          on: {
            no: {
              target: 'usingGLP1',
              actions: {
                type: 'storeQuestionnaire',
                params: { isPregnant: 'no' },
              },
            },
            yes: {
              target: 'rejectedIsPregnant',
              actions: {
                type: 'storeQuestionnaire',
                params: { isPregnant: 'yes' },
              },
            },
            back: { target: 'gender' },
          },
          meta: {
            step: 7,
            question: QUESTIONNAIRE_QUESTIONS.isPregnant,
            content: {},
          },
        },
        rejectedIsPregnant: {
          entry: [
            { type: 'reject', params: { reason: 'is pregnant' } },
            { type: 'track', params: { eventName: 'OnboardingReject' } },
          ],
          on: {
            back: {
              target: 'isPregnant',
              actions: 'clearRejected',
            },
          },
          meta: {
            step: 7,
            content: {},
          },
        },
        usingGLP1: {
          exit: [
            {
              type: 'identify',
              params: { trait: 'usingGLP1', source: 'questionnaire' },
            },
          ],
          on: {
            no: {
              target: 'haveDiabetes',
              actions: {
                type: 'storeQuestionnaire',
                params: { usingGLP1: 'no' },
              },
            },
            yes: {
              target: 'haveDiabetes',
              actions: {
                type: 'storeQuestionnaire',
                params: { usingGLP1: 'yes' },
              },
            },
            back: { target: 'gender' },
          },
          meta: {
            step: 7,
            question: QUESTIONNAIRE_QUESTIONS.usingGLP1,
            content: {},
          },
        },
        haveDiabetes: {
          exit: [
            {
              type: 'identify',
              params: { trait: 'haveDiabetes', source: 'questionnaire' },
            },
          ],
          on: {
            no: {
              target: 'eligible',
              actions: {
                type: 'storeQuestionnaire',
                params: { haveDiabetes: 'no' },
              },
            },
            yes: {
              target: 'rejectedPriorConditions',
              actions: {
                type: 'storeQuestionnaire',
                params: { haveDiabetes: 'yes' },
              },
            },
            back: { target: 'usingGLP1' },
          },
          meta: {
            step: 7,
            question: QUESTIONNAIRE_QUESTIONS.haveDiabetes,
            content: {},
          },
        },
        rejectedPriorConditions: {
          entry: [
            { type: 'reject', params: { reason: 'has prior conditions' } },
            { type: 'track', params: { eventName: 'OnboardingReject' } },
          ],
          on: {
            back: { target: 'haveDiabetes', actions: 'clearRejected' },
          },
          meta: {
            step: 7,
            content: {},
          },
        },
        eligible: {
          entry: [
            { type: 'track', params: { eventName: 'VisitStarted' } },
            { type: 'log', params: { action: 'PATIENT_VISIT_STARTED' } },
          ],
          on: {
            back: { target: 'haveDiabetes' },
            next: { target: 'doctorVisits' },
          },
          meta: {
            step: 8,
            content: {},
          },
        },
        doctorVisits: {
          on: {
            no: {
              target: 'recommendSeeDoctor',
              actions: {
                type: 'storeQuestionnaire',
                params: { doctorVisits: 'no' },
              },
            },
            yes: {
              target: 'qualifyingConditions',
              actions: {
                type: 'storeQuestionnaire',
                params: { doctorVisits: 'yes' },
              },
            },
            back: { target: 'eligible' },
          },
          meta: {
            step: 9,
            question: QUESTIONNAIRE_QUESTIONS.doctorVisits,
            content: {},
          },
        },
        recommendSeeDoctor: {
          on: {
            back: { target: 'doctorVisits' },
            next: { target: 'qualifyingConditions' },
          },
          meta: {
            step: 9,
            content: {},
          },
        },
        qualifyingConditions: {
          on: {
            back: { target: 'doctorVisits' },
            next: { target: 'height', actions: 'storeQuestionnaire' },
          },
          meta: {
            step: 10,
            question: QUESTIONNAIRE_QUESTIONS.qualifyingConditions,
            content: {},
          },
        },
        height: {
          on: {
            back: { target: 'qualifyingConditions' },
            next: { target: 'weight', actions: 'storeQuestionnaire' },
          },
          meta: {
            step: 11,
            question: QUESTIONNAIRE_QUESTIONS.height,
            content: {},
          },
        },
        weight: {
          exit: [
            {
              type: 'identify',
              params: { trait: 'weight', source: 'questionnaire' },
            },
          ],
          on: {
            back: { target: 'height' },
            next: { target: 'desiredWeight', actions: 'calculateAndStoreBMI' },
          },
          meta: {
            step: 12,
            question: QUESTIONNAIRE_QUESTIONS.weight,
            content: {},
          },
        },
        desiredWeight: {
          exit: [
            {
              type: 'identify',
              params: { trait: 'desiredWeight', source: 'questionnaire' },
            },
          ],
          on: {
            back: { target: 'weight' },
            next: { target: 'objectives', actions: 'storeQuestionnaire' },
          },
          meta: {
            step: 13,
            question: QUESTIONNAIRE_QUESTIONS.desiredWeight,
            content: {},
          },
        },
        objectives: {
          exit: [
            {
              type: 'track',
              params: { eventName: 'necessityStatementComplete' },
            },
          ],
          on: {
            back: { target: 'desiredWeight' },
            next: [
              {
                guard: 'needsMedicalNecessity',
                target: 'rejectedMedicalNecessity',
                actions: 'storeQuestionnaire',
              },
              {
                target: 'haveAllergies',
                actions: 'storeQuestionnaire',
              },
            ],
          },
          meta: {
            step: 14,
            question: QUESTIONNAIRE_QUESTIONS.objectives,
            content: {},
          },
        },
        rejectedMedicalNecessity: {
          entry: [
            { type: 'reject', params: { reason: 'medical necessity' } },
            { type: 'track', params: { eventName: 'OnboardingReject' } },
          ],
          on: {
            back: { target: 'objectives', actions: 'clearRejected' },
          },
          meta: {
            step: 14,
            content: {},
          },
        },
        haveAllergies: {
          on: {
            no: {
              target: 'medications',
              actions: {
                type: 'storeQuestionnaire',
                params: { allergies: [], hasAllergies: 'no' },
              },
            },
            yes: {
              target: 'selectAllergies',
              actions: {
                type: 'storeQuestionnaire',
                params: { hasAllergies: 'yes' },
              },
            },
            back: { target: 'objectives' },
          },
          meta: {
            step: 15,
            question: QUESTIONNAIRE_QUESTIONS.hasAllergies,
            valueMap: {
              hasAllergies: 'questionnaire.hasAllergies',
            },
            content: {},
          },
        },
        selectAllergies: {
          on: {
            back: { target: 'haveAllergies' },
            next: { target: 'medications', actions: 'storeQuestionnaire' },
          },
          meta: {
            step: 15,
            question: QUESTIONNAIRE_QUESTIONS.allergies,
            valueMap: {
              allergies: 'questionnaire.allergies',
            },
            content: {},
          },
        },
        medications: {
          on: {
            back: [
              {
                guard: ({ context }) =>
                  context.questionnaire.hasAllergies === 'yes',
                target: 'selectAllergies',
              },
              { target: 'haveAllergies' },
            ],
            next: {
              target: 'medicalConditions',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 16,
            question: QUESTIONNAIRE_QUESTIONS.medications,
            content: {},
          },
        },
        medicalConditions: {
          on: {
            back: { target: 'medications' },
            next: {
              target: 'additionalInformation',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 17,
            question: QUESTIONNAIRE_QUESTIONS.medicalConditions,
            content: {},
          },
        },
        additionalInformation: {
          on: {
            back: { target: 'medicalConditions' },
            next: { target: 'finished', actions: 'storeQuestionnaire' },
          },
          meta: {
            step: 18,
            question: QUESTIONNAIRE_QUESTIONS.additionalInformation,
            content: {},
          },
        },
        finished: {
          entry: [
            { type: 'complete', params: { completed: true } },
            { type: 'track', params: { eventName: 'QuestionnaireCompleted' } },
            {
              type: 'log',
              params: { action: 'ONBOARDING_QUESTIONNAIRE_COMPLETED' },
            },
          ],
          always: `#onboarding-${version}.selectTreatmentType`,
        },
      },
    },
    selectTreatmentType: {
      on: {
        submit: {
          target: 'validatingTreatmentType',
        },
        back: { target: 'questionnaire.additionalInformation' },
      },
      meta: {
        step: 1,
        name: 'Virtual Doctor Visit',
        content: {},
      },
    },
    validatingTreatmentType: {
      invoke: {
        src: 'validateTreatmentType',
        input: ({ event, context }) => ({
          context,
          treatmentType: (event as any).value?.treatmentType,
        }),
        onDone: {
          target: 'selectTreatment',
          actions: assign(({ event }) => ({
            treatmentType: event.output.treatmentType,
            pharmacyId: event.output.pharmacyId,
          })),
        },
        onError: {
          target: 'selectTreatmentType',
          actions: 'assignValidationError',
        },
      },
    },
    selectTreatment: {
      on: {
        submit: {
          target: 'validatingTreatment',
        },
        back: { target: 'selectTreatmentType' },
      },
      meta: {
        step: 2,
        name: 'Virtual Doctor Visit',
        valueMap: { products: 'products' },
        content: {},
      },
    },
    validatingTreatment: {
      exit: [{ type: 'track', params: { eventName: 'TreatmentChosen' } }],
      invoke: {
        src: 'validateTreatment',
        input: ({ event, context }) => ({
          context,
          products: (event as any).value?.products,
          vials: (event as any).value?.vials,
        }),
        onDone: [
          {
            guard: 'hasSsnSuccess',
            target: 'uploadFacePhoto',
            actions: {
              type: 'storeProductsFromActor',
              params: ({ event }) => ({ output: event.output }),
            },
          },
          {
            guard: 'hasSsnCheckFailed',
            target: 'uploadIDPhoto',
            actions: {
              type: 'storeProductsFromActor',
              params: ({ event }) => ({ output: event.output }),
            },
          },
          {
            target: 'identityVerification',
            actions: {
              type: 'storeProductsFromActor',
              params: ({ event }) => ({ output: event.output }),
            },
          },
        ],
        onError: {
          target: 'selectTreatment',
          actions: 'assignValidationError',
        },
      },
    },
    identityVerification: {
      entry: [
        {
          type: 'track',
          params: { eventName: 'IdentityVerificationStarted' },
        },
      ],
      on: {
        next: { target: 'ssnCheck', actions: 'clearSsnVerificationState' },
        back: { target: 'selectTreatment' },
      },
      meta: {
        step: 3,
        name: 'Identity Verification',
        content: {},
      },
    },
    ssnCheck: {
      on: {
        submit: { target: 'verifySsn', actions: 'storeSsnData' },
        skip: { target: 'uploadIDPhoto' },
        back: { target: 'identityVerification' },
      },
      meta: {
        step: 4,
        name: 'Identity Verification',
        content: {},
      },
    },
    verifySsn: {
      entry: [
        { type: 'track', params: { eventName: 'VerificationServiceSubmit' } },
      ],
      invoke: {
        src: 'verifySsn',
        input: ({ context, event }) => {
          const lastFourSSN =
            event.type === 'submit' && event.value?.lastFourSSN
              ? event.value.lastFourSSN
              : context.lastFourSSN;

          return {
            lastFourSSN,
            patientId: context['patientId'],
            firstName: context.firstName,
            lastName: context.lastName,
            phone: context.phone,
            dateOfBirth: context.questionnaire.birthDate,
          };
        },
        onDone: {
          target: 'uploadFacePhoto',
          actions: [
            {
              type: 'storeSsnSuccess',
              params: ({ event }) => ({ output: event.output }),
            },
            {
              type: 'track',
              params: { eventName: 'VerificationServiceAccept' },
            },
          ],
        },
        onError: {
          target: 'uploadIDPhoto',
          actions: [
            'storeSsnFailure',
            'assignValidationError',
            {
              type: 'track',
              params: { eventName: 'VerificationServiceReject' },
            },
          ],
        },
      },
    },
    uploadIDPhoto: {
      exit: [
        { type: 'identify', params: { trait: 'idPhoto', source: 'idPhoto' } },
        { type: 'track', params: { eventName: 'IDUploaded' } },
      ],
      on: {
        next: {
          target: 'uploadFacePhoto',
          actions: [
            'store',
            {
              type: 'log',
              params: { action: 'ID_PHOTO_EVENT', type: 'id-photo' },
            },
          ],
        },
        back: { target: 'selectTreatment' },
      },
      meta: {
        step: 7,
        name: 'Virtual Doctor Visit',
        valueMap: { 'id-photo': 'id-photo' },
        content: {},
      },
    },
    uploadFacePhoto: {
      exit: [
        {
          type: 'identify',
          params: { trait: 'facePhoto', source: 'facePhoto' },
        },
        { type: 'track', params: { eventName: 'PhotoUploaded' } },
        {
          type: 'track',
          params: { eventName: 'IdentityVerificationComplete' },
        },
      ],
      on: {
        next: {
          target: 'visitCompletion',
          actions: [
            'store',
            {
              type: 'log',
              params: { action: 'FACE_PHOTO_EVENT', type: 'face-photo' },
            },
          ],
        },
        back: [
          {
            guard: 'hasSsnSuccess',
            target: 'selectTreatment',
          },
          {
            target: 'uploadIDPhoto',
          },
        ],
      },
      meta: {
        step: 8,
        name: 'Virtual Doctor Visit',
        valueMap: { 'face-photo': 'face-photo' },
        content: {},
      },
    },
    visitCompletion: {
      on: {
        next: { target: 'summary' },
        back: { target: 'uploadFacePhoto' },
      },
      meta: {
        step: 9,
        name: 'Checkout',
        content: {},
      },
    },
    summary: {
      entry: [{ type: 'track', params: { eventName: 'VisitSummary' } }],
      on: {
        next: { target: 'shipping' },
        back: { target: 'visitCompletion' },
      },
      meta: {
        step: 10,
        name: 'Checkout',
        valueMap: { products: 'products' },
        content: {},
      },
    },
    shipping: {
      on: {
        next: { target: 'validateAndSaveShipping', actions: 'store' },
        back: { target: 'summary' },
      },
      meta: {
        step: 11,
        name: 'Checkout',
        content: {},
      },
    },
    validateAndSaveShipping: {
      invoke: {
        src: 'validateShipping',
        input: ({ context }) => ({
          context,
          shippingInfo: context.shippingInfo,
        }),
        onDone: {
          target: 'payment',
        },
        onError: {
          target: 'shipping',
          actions: 'assignValidationError',
        },
      },
    },
    payment: {
      entry: [
        { type: 'track', params: { eventName: 'CheckoutStarted' } },
        { type: 'log', params: { action: 'ONBOARDING_CHECKOUT_STARTED' } },
      ],
      on: {
        complete: { target: 'processingPayment' },
        back: { target: 'shipping' },
      },
      meta: {
        step: 12,
        name: 'Checkout',
        content: {},
      },
    },
    processingPayment: {
      on: {
        complete: { target: 'onboarded' },
      },
      meta: {
        step: 12,
        name: 'Checkout',
        content: {},
      },
    },
    onboarded: {
      type: 'final',
      entry: [
        { type: 'track', params: { eventName: 'CheckoutComplete' } },
        { type: 'track', params: { eventName: 'OnboardingComplete' } },
        {
          type: 'identify',
          params: { trait: 'paymentMethod', source: 'paymentMethod' },
        },
        { type: 'log', params: { action: 'ONBOARDING_CHECKOUT_COMPLETED' } },
      ],
      meta: {
        step: 13,
        name: 'Checkout',
        content: {},
      },
    },
  },
});
