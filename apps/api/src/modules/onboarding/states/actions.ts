import type {
  PostQuestionnaireData,
  PreSignupData,
  QuestionnaireStepData,
} from './types/onboarding-generic.types';

// Pure helper functions for action logic
// These contain the business logic without XState-specific types

export const getRejectPayload = (reason: string) => ({
  rejected: true,
  rejectedReason: reason,
});

export const getClearRejectedPayload = () => ({
  rejected: false,
  rejectedReason: undefined,
});

export const getCompletePayload = () => ({
  questionnaireCompleted: true,
});

export const mergeQuestionnaireData = (
  context: any,
  event: any,
  params?: any,
) => {
  // For 'next' events, merge the event value data
  const data =
    event.type === 'next' && event.value
      ? (event.value as QuestionnaireStepData)
      : {};

  // Always merge params (for radio button selections) and data
  const updatedQuestionnaire = {
    ...context.questionnaire,
    ...data,
    ...params,
  };

  return {
    ...context,
    questionnaire: updatedQuestionnaire,
  };
};

export const mergeStoreData = (context: any, event: any) => {
  const data = event.value as PostQuestionnaireData;
  return { ...context, ...data };
};

export const storeProductsFromActor = (
  context: any,
  params: {
    output: {
      success: boolean;
      products: Array<{
        id: string;
        name: string;
        label: string;
        image: string;
        price: number;
        priceId: string;
        type: 'core' | 'additional';
        supplyLength: string;
        form: string;
        dosageLabel: string;
        description?: string;
      }>;
    };
  },
) => ({
  ...context,
  products: params.output.products,
});

export const mergePreSignupData = (context: any, event: any) => {
  if (event.type === 'next' && event.value) {
    const data = event.value as PreSignupData;
    return { ...context, ...data };
  }

  if (event.type === 'submit') {
    const { password, ...valueWithoutPassword } = event.value || {};
    return { ...context, ...valueWithoutPassword };
  }

  return context;
};

// V2-specific SSN helpers
export const mergeSsnData = (context: any, event: any) => {
  if (event.type === 'submit' && event.value.lastFourSSN) {
    return {
      ...context,
      lastFourSSN: event.value.lastFourSSN,
      vouchedVerifiedAt: new Date(),
    };
  }
  return context;
};

export const getSsnSuccessPayload = (
  context: any,
  output: { success: boolean; verificationId?: string },
) => ({
  ...context,
  ssnVerified: output.success,
  ssnCheckFailed: false,
  ssnVerificationId: output.verificationId,
});

export const getSsnFailurePayload = (context: any) => ({
  ...context,
  ssnVerified: false,
  ssnCheckFailed: true,
  ssnVerificationId: undefined,
});

export const getClearSsnVerificationPayload = (context: any) => ({
  ...context,
  ssnVerified: false,
  ssnCheckFailed: false,
  ssnVerificationId: undefined,
});

// V3-specific BMI calculation helper
export const calculateAndStoreBMI = (context: any, event: any) => {
  if (event.type !== 'next' || !event.value) return context;

  const weight = event.value.weight; // in pounds
  const height = context.questionnaire.height; // in inches

  if (!weight || !height) return context;

  // Convert pounds to kg and inches to cm, then to meters
  const weightInKg = weight / 2.2046;
  const heightInCm = height * 2.54;
  const heightInM = heightInCm / 100;

  // Calculate BMI: weight(kg) / height(m)²
  const bmi = Number((weightInKg / (heightInM * heightInM)).toFixed(2));

  return {
    ...context,
    bmi,
    questionnaire: {
      ...context.questionnaire,
      weight,
    },
  };
};

// Reset context helper for deprecated onboarding migration
// This is ONLY used when resetting deprecated onboarding versions to the latest version
// It creates a fresh context with data extracted from the database
export const getResetContextPayload = (
  _context: any,
  params?: { data?: any },
) => {
  return {
    // Base fields for fresh questionnaire state
    questionnaireCompleted: false,
    questionnaire: {},
    rejected: false,
    rejectedReason: undefined,
    // Inject data from database (email, firstName, phone, etc.)
    ...params?.data,
  };
};

// Discount attachment helper
export const attachDiscountPayload = (
  context: any,
  params?: {
    discount?: {
      type: 'referral' | 'coupon';
      code: string;
      label: string;
      value: number;
      valueType: 'percentage' | 'fixed';
    };
  },
) => {
  // Return unchanged context if no discount provided
  if (!params?.discount) {
    return context;
  }

  // For referral codes: don't replace if one already exists
  if (
    params.discount.type === 'referral' &&
    context.discount?.type === 'referral'
  ) {
    return context;
  }

  // For promo coupons: always allow replacement
  return {
    ...context,
    discount: params.discount,
  };
};
