/**
 * Re-export everything from the unified configuration and types
 * This maintains backward compatibility while centralizing all version management
 */
export {
  ONBOARDING_MACHINE_REGISTRY,
  VERSION_ORDER,
  VERSION_FEATURES,
  type OnboardingContextMap,
  type OnboardingFeatures,
} from './onboarding-versions.config';

export {
  type OnboardingVersion,
  type AnyOnboardingInput,
} from './types/onboarding-generic.types';
