import type { OnboardingContext } from './types/onboarding-generic.types';
import { onboardingV1Machine } from './onboarding-v1.state';
import { onboardingV2Machine } from './onboarding-v2.state';
import { onboardingV3Machine } from './onboarding-v3.state';
import { onboardingV4Machine } from './onboarding-v4.state';

/**
 * Feature capabilities interface
 * Only define features that are present - absence means the feature is not available
 */
export interface OnboardingFeatures {
  hasSSNVerification?: boolean;
  hasBMICalculation?: boolean;
  hasObjectivesInQuestionnaire?: boolean;
}

/**
 * Unified version configuration interface
 * This is the single source of truth for all onboarding version metadata
 */
export interface OnboardingVersionConfig<
  TVersion extends string = string,
  TMachine = any,
> {
  /** Version identifier */
  version: TVersion;
  /** Numeric priority for ordering (higher = newer) */
  priority: number;
  /** Whether this version is deprecated and should be upgraded */
  deprecated: boolean;
  /** Whether this is the default version for new patients */
  isDefault: boolean;
  /** Feature capabilities for this version */
  features: OnboardingFeatures;
  /** XState machine instance */
  machine: TMachine;

  // A/B Testing fields
  /** Group ID for A/B testing - versions with same groupId are alternatives */
  groupId?: string;
  /** Weight for random distribution within group (defaults to equal split) */
  weight?: number;
  /** Whether this is the default/fallback variant within the group */
  groupDefault?: boolean;
}

/**
 * Comprehensive onboarding versions registry
 * All version metadata is defined here in priority order (lowest to highest)
 *
 * Adding a new version:
 * 1. Add the machine import at the top
 * 2. Add types imports for context and input
 * 3. Add a new entry to this array with incremented priority
 * 4. Update isDefault flag if this should be the new default
 * 5. TypeScript will enforce consistency across the codebase
 */
export const ONBOARDING_VERSIONS_CONFIG: OnboardingVersionConfig[] = [
  {
    version: 'legacy-v1',
    priority: 0,
    deprecated: true,
    isDefault: false,
    machine: null,
    features: null,
  },
  {
    version: 'legacy-v2',
    priority: 0,
    deprecated: true,
    isDefault: false,
    machine: null,
    features: null,
  },
  {
    version: 'v1',
    priority: 1,
    deprecated: false,
    isDefault: false,
    features: {
      hasObjectivesInQuestionnaire: true,
    },
    machine: onboardingV1Machine,
  },
  {
    version: 'v2',
    priority: 2,
    deprecated: false,
    isDefault: false,
    features: {
      hasObjectivesInQuestionnaire: true,
      hasSSNVerification: true,
    },
    machine: onboardingV2Machine,
  },
  {
    version: 'v3',
    priority: 3,
    deprecated: false,
    isDefault: true,
    features: {
      hasObjectivesInQuestionnaire: true,
      hasSSNVerification: true,
      hasBMICalculation: true,
    },
    machine: onboardingV3Machine,
  },
  {
    version: 'v4',
    priority: 4,
    deprecated: false,
    isDefault: false,
    features: {
      hasObjectivesInQuestionnaire: true,
      hasSSNVerification: true,
      hasBMICalculation: true,
    },
    machine: onboardingV4Machine,
    groupId: 'v4',
    weight: 0.5,
  },
  {
    version: 'v4a',
    priority: 5,
    deprecated: false,
    isDefault: false,
    features: {
      hasObjectivesInQuestionnaire: true,
      hasSSNVerification: true,
      hasBMICalculation: true,
    },
    machine: onboardingV4Machine,
    groupId: 'v4',
    weight: 0.5,
  },
];

/**
 * Type mapping for onboarding version to context type
 */
export type OnboardingContextMap = {
  v1: OnboardingContext;
  v2: OnboardingContext;
  v3: OnboardingContext;
  v4: OnboardingContext;
};

/**
 * Machine registry derived from configuration
 */
export const ONBOARDING_MACHINE_REGISTRY = Object.fromEntries(
  ONBOARDING_VERSIONS_CONFIG.map((config) => [config.version, config.machine]),
) as Record<string, any>;

/**
 * Version order mapping derived from configuration
 */
export const VERSION_ORDER = Object.fromEntries(
  ONBOARDING_VERSIONS_CONFIG.map((config) => [config.version, config.priority]),
) as Record<string, number>;

/**
 * Version features mapping derived from configuration
 */
export const VERSION_FEATURES = Object.fromEntries(
  ONBOARDING_VERSIONS_CONFIG.map((config) => [config.version, config.features]),
) as Record<string, OnboardingFeatures>;
