import { fromPromise } from 'xstate';

import type {
  AddToWaitingListInput,
  AddToWaitingListOutput,
  CheckStateEnabledInput,
  CheckStateEnabledOutput,
  CreateUserInput,
  CreateUserOutput,
  OnboardingContext,
  ValidateNameInput,
  ValidateNameOutput,
  VerifySsnInput,
  VerifySsnOutput,
} from './types/onboarding-generic.types';

// Actor schemas for type checking only - actual implementations provided by factory
export const actorSchemas = {
  checkStateEnabled: fromPromise<
    CheckStateEnabledOutput,
    CheckStateEnabledInput
  >(async () => {
    throw new Error('Actor implementation provided at runtime');
  }),
  validateName: fromPromise<ValidateNameOutput, ValidateNameInput>(async () => {
    throw new Error('Actor implementation provided at runtime');
  }),
  createUser: fromPromise<CreateUserOutput, CreateUserInput>(async () => {
    throw new Error('Actor implementation provided at runtime');
  }),
  addToWaitingList: fromPromise<AddToWaitingListOutput, AddToWaitingListInput>(
    async () => {
      throw new Error('Actor implementation provided at runtime');
    },
  ),
  verifySsn: fromPromise<VerifySsnOutput, VerifySsnInput>(async () => {
    throw new Error('Actor implementation provided at runtime');
  }),
  validateTreatmentType: fromPromise<
    { treatmentType: string; pharmacyId: string },
    { context: OnboardingContext }
  >(async () => {
    throw new Error('Actor implementation provided at runtime');
  }),
  validateTreatment: fromPromise<
    {
      success: boolean;
      products: Array<{
        id: string;
        name: string;
        label: string;
        image: string;
        price: number;
        priceId: string;
        type: 'core' | 'additional';
        supplyLength: string;
        form: string;
        dosageLabel: string;
        description?: string;
      }>;
    },
    { context: OnboardingContext; products?: string | string[] }
  >(async () => {
    throw new Error('Actor implementation provided at runtime');
  }),
  validateShipping: fromPromise<
    { valid: boolean },
    { context: OnboardingContext }
  >(async () => {
    throw new Error('Actor implementation provided at runtime');
  }),
  validateAndSaveShipping: fromPromise<
    { success: boolean },
    { context: OnboardingContext }
  >(async () => {
    throw new Error('Actor implementation provided at runtime');
  }),
  setupPaymentIntent: fromPromise<
    { paymentIntent: string },
    { context: OnboardingContext }
  >(async () => {
    throw new Error('Actor implementation provided at runtime');
  }),
};
