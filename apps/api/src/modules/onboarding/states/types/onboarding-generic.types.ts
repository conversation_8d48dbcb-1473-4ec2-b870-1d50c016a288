import type { ShippingInfoDto } from '@modules/onboarding/dto/shipping-info.dto';

import type { ONBOARDING_VERSIONS_CONFIG } from '../onboarding-versions.config';

// Generic types that are shared between onboarding versions

// Pre-signup data types
export interface PreSignupData {
  state?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  password?: string;
  getPromotionsSMS?: boolean;
  promoCoupon?: string;
  referralCode?: string;
}

// Base questionnaire step data types (without objectives - used by Legacy V1)
export interface BaseQuestionnaireStepData {
  birthDate?: string;
  gender?: 'male' | 'female';
  height?: number;
  weight?: number;
  desiredWeight?: number;
  usingGLP1?: 'yes' | 'no';
  isPregnant?: 'yes' | 'no';
  haveDiabetes?: 'yes' | 'no';
  doctorVisits?: 'yes' | 'no';
  hasAllergies?: 'yes' | 'no';
  allergies?: string[];
  medications?: string[];
  medicalConditions?: string[];
  qualifyingConditions?: string[];
  additionalInformation?: string;
  priorConditions?: string[];
}

// Extended questionnaire step data types (with objectives - used by Legacy V2, V1, V2)
export interface ExtendedQuestionnaireStepData
  extends BaseQuestionnaireStepData {
  objectives?: string[];
}

// Backward compatibility alias
export type QuestionnaireStepData = ExtendedQuestionnaireStepData;

// Product type used in onboarding context
export interface OnboardingProduct {
  id: string;
  name: string;
  label: string;
  image: string;
  price: number;
  priceId: string;
  type: 'core' | 'additional';
  supplyLength: string;
  form: string;
  dosageLabel: string;
  description?: string;
}

// Post-questionnaire data types
export interface PostQuestionnaireData {
  productType?: string;
  treatmentType?: string;
  products?: OnboardingProduct[];
  'id-photo'?: string;
  'face-photo'?: string;
  shippingInfo?: ShippingInfoDto;
  paymentIntent?: string;
}

// Actor input/output types
export interface CheckStateEnabledInput {
  state?: string;
}

export interface CheckStateEnabledOutput {
  state: string;
  enabled: boolean;
  reason?: string;
}

export interface AddToWaitingListInput {
  email: string;
  state: string;
}

export interface AddToWaitingListOutput {
  success: boolean;
}

export interface ValidateNameInput {
  firstName: string;
  lastName: string;
}

export interface ValidateNameOutput {
  valid: boolean;
  errors?: string[];
}

export interface CreateUserOutput {
  success: boolean;
  authData?: any;
}

// SSN verification types (shared between V2+)
export interface VerifySsnInput {
  lastFourSSN: string;
  firstName: string;
  lastName: string;
  phone: string;
  dateOfBirth?: string;
}

export interface VerifySsnOutput {
  success: boolean;
  verificationId?: string;
}

// Unified context type that contains all fields from all onboarding versions
export interface OnboardingContext
  extends PreSignupData,
    PostQuestionnaireData {
  // State management
  rejected?: boolean;
  questionnaireCompleted: boolean;
  rejectedReason?: string;

  // Auth data (populated after user creation)
  authData?: any;

  // Questionnaire data (structured)
  questionnaire: Partial<QuestionnaireStepData>;

  // Additional post-questionnaire data
  pharmacyId?: string;

  // Discount tracking (attached via query params)
  discount?: {
    type: 'referral' | 'coupon';
    code: string;
    label: string;
    value: number;
    valueType: 'percentage' | 'fixed';
  };

  // SSN verification data (V2+)
  ssnVerified?: boolean;
  ssnCheckFailed?: boolean;
  ssnVerificationId?: string;
  lastFourSSN?: string;
  vouchedVerifiedAt?: Date;

  // BMI calculation (V3+)
  bmi?: number;

  // Validation errors from actors
  validationError?: {
    type: string;
    message: string;
    errors: Array<{
      field: string;
      message: string;
    }>;
  } | null;
}

// Backward compatibility alias
export type BaseOnboardingContext = OnboardingContext;

// Legacy base context (used by legacy versions without pre-signup flow)
export interface BaseLegacyOnboardingContext<
  TQuestionnaire = ExtendedQuestionnaireStepData,
> extends PostQuestionnaireData {
  // State management
  rejected?: boolean;
  questionnaireCompleted: boolean;
  rejectedReason?: string;

  // Questionnaire data (structured)
  questionnaire: Partial<TQuestionnaire>;

  // Additional post-questionnaire data
  pharmacyId?: string;
}

// Base event types shared across all onboarding versions
export type BaseEvents = { type: 'reset'; data?: any };

// Navigation events (common to all versions)
export type BaseNavigationEvents =
  | {
      type: 'next';
      value?: PreSignupData | QuestionnaireStepData | PostQuestionnaireData;
    }
  | { type: 'back' };

// Questionnaire-specific answer events (common to all versions)
export type BaseQuestionnaireEvents =
  | { type: 'male' }
  | { type: 'female' }
  | { type: 'yes' }
  | { type: 'no' };

// Submit events (simplified)
export type BaseSubmitEvents = {
  type: 'submit';
  value?: any;
};

// Payment events (used by all versions in payment state)
export type BasePaymentEvents = { type: 'update' } | { type: 'complete' };

// SSN-specific events (V2+)
export type SSNEvents = BaseSubmitEvents | { type: 'skip' };

// Composed event types for reuse
export type BaseOnboardingEvents =
  | BaseEvents
  | BaseNavigationEvents
  | BaseQuestionnaireEvents
  | BasePaymentEvents;

// Unified events type that includes all possible events from all versions
export type OnboardingEvents =
  | BaseOnboardingEvents
  | BaseSubmitEvents
  | SSNEvents;

export type LegacyOnboardingEvents = BaseOnboardingEvents;

// Unified input type for machine initialization
export interface OnboardingInput {
  version: string;
  existingData?: Partial<OnboardingContext>;
  statePersistence?: any;
}

// Unified actor input/output types for type-safe invocations
export interface CreateUserInput {
  context: OnboardingContext;
}

/**
 * Derived onboarding version type from configuration
 */
export type OnboardingVersion =
  (typeof ONBOARDING_VERSIONS_CONFIG)[number]['version'];

/**
 * Union type of all possible machine inputs
 */
export type AnyOnboardingInput = OnboardingInput;
