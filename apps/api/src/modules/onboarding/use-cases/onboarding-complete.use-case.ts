import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { ReferralService } from '@/modules/referral/referral.service';
import { SegmentService } from '@/modules/segment/segment.service';
import { PatientPaymentMethodPersistence } from '@adapters/persistence/database/patient-payment-method.persistence';
import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { StatePersistence } from '@adapters/persistence/database/state.persistence';
import { OnboardingVersionManager } from '@modules/onboarding/helpers/onboarding-version-manager';
import { OnboardingService } from '@modules/onboarding/onboarding.service';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states';
import { ShippingInfo } from '@modules/onboarding/types/shipping-info.type';
import { Injectable } from '@nestjs/common';
import { patientGender, patientStatus, Prisma } from '@prisma/client';
import Stripe from 'stripe';

import { MedicalNecessityService } from '../services/medical-necessity.service';

@Injectable()
export class OnboardingCompleteUseCase {
  constructor(
    private readonly patientPersistence: PatientPersistence,
    private readonly statePersistence: StatePersistence,
    private readonly onboardingStateService: OnboardingStateService,
    private readonly versionManager: OnboardingVersionManager,
    private readonly onboardingService: OnboardingService,
    private readonly patientPaymentMethodPersistence: PatientPaymentMethodPersistence,
    private readonly prismaService: PrismaService,
    private readonly medicalNecessityService: MedicalNecessityService,
    private readonly segment: SegmentService,
    private readonly referralService: ReferralService,
  ) {}

  async execute(
    profile: any,
    actor: any,
    event: Stripe.PaymentMethodAttachedEvent,
  ) {
    try {
      const state = actor.getSnapshot().value;
      if (!['processingPayment', 'payment'].includes(state)) {
        throw new Error('Invalid onboarding state' + state);
      }

      await runInDbTransaction(this.prismaService, async (prisma) => {
        // Set up event listeners to collect emitted events
        const emittedEvents = this.onboardingService.setupEventListeners(actor);

        // There's a possible race condition where stripe can send the event before user sends the last event to complete onboarding
        // in that case we need to move the state machine to processingPayment first
        if (actor.getSnapshot().value === 'payment')
          actor.send({ type: 'complete' });

        actor.send({ type: 'complete' });

        // Get the snapshot after complete event
        const snapshot = actor.getSnapshot();

        // Process all collected events
        await this.onboardingService.processEvents(emittedEvents);
        const context = snapshot.context;

        // persist desired products
        await prisma.patientDesiredTreatment.createMany({
          data: context.products.map((treatment) => ({
            patientId: profile.id,
            productId: treatment.id,
            vials: 1,
          })),
        });

        const state = await this.statePersistence.getByCode(
          context.shippingInfo.state,
        );
        const shippingStateChanged = state.id !== profile.state.id;

        // persist shipping address
        const shipping: ShippingInfo = {
          address1: context.shippingInfo.address1,
          address2: context.shippingInfo.address2,
          city: context.shippingInfo.city,
          stateId: state.id,
          zip: context.shippingInfo.zip.toString(),
        };
        await prisma.patientShippingAddress.create({
          data: {
            patientId: profile.id,
            ...shipping,
            default: true,
          },
        });

        // persist payment method
        await this.patientPaymentMethodPersistence.create({
          patient: {
            connect: { id: profile.id },
          },
          stripeId: event.data.object.id,
          data: event.data.object,
          type: event.data.object.type,
          default: true,
        });

        // update patient profile
        const questionnaire = context.questionnaire;
        const birthDate = new Date(questionnaire.birthDate!);
        const gender = questionnaire.gender as patientGender;
        const height = questionnaire.height!;
        const weight = questionnaire.weight!;
        const idPhoto = context['id-photo'];
        const facePhoto = context['face-photo'];

        const onboardingVersion =
          profile.onboardingVersion as OnboardingVersion;

        const isDocumentVerified =
          this.versionManager.hasCapability(
            onboardingVersion,
            'hasSSNVerification',
          ) && (context as any).ssnVerified === true;

        // Determine status based on verification type
        const status: patientStatus = isDocumentVerified
          ? facePhoto
            ? 'onboardingCompleted'
            : 'pendingUploadPhotos'
          : idPhoto && facePhoto
            ? 'onboardingCompleted'
            : 'pendingUploadPhotos';

        // Build update data including pharmacy if override exists and state if changed
        const updateData: Omit<Prisma.PatientUpdateInput, 'onboardingState'> = {
          status,
          birthDate,
          gender,
          height,
          weight,
          idPhoto,
          facePhoto,
          completedAt: new Date(),
        };

        if (
          this.versionManager.hasCapability(
            onboardingVersion,
            'hasSSNVerification',
          )
        ) {
          updateData.identityVerificationType = isDocumentVerified
            ? 'document'
            : 'photoId';

          if (isDocumentVerified) {
            updateData.lastFourSSN = (context as any).lastFourSSN;
            updateData.vouchedVerificationId = (
              context as any
            ).ssnVerificationId;
            updateData.vouchedVerifiedAt = (context as any).vouchedVerifiedAt;
          }
        }

        // Add pharmacy update if override exists in context
        if (context.pharmacyId && context.pharmacyId !== profile.pharmacy?.id) {
          Object.assign(updateData, {
            pharmacy: { connect: { id: context.pharmacyId } },
          });
        }

        if (shippingStateChanged) {
          Object.assign(updateData, { state: { connect: { id: state.id } } });

          await this.segment.identify(
            profile.id,
            {
              traits: { state: context.shippingInfo.state },
            },
            { prisma, source: 'OnboardingCompleteUseCase' },
          );
        }

        await this.patientPersistence.updateOnboarding(
          profile.id,
          actor.getPersistedSnapshot(),
          updateData,
          prisma,
        );

        // Store medical necessities for versions that support objectives
        const objectives = (context.questionnaire as any).objectives;
        if (objectives && Array.isArray(objectives)) {
          const bmi = this.versionManager.hasCapability(
            onboardingVersion,
            'hasBMICalculation',
          )
            ? context.bmi
            : undefined;

          await this.medicalNecessityService.createPatientMedicalNecessities(
            profile.id,
            objectives,
            bmi,
            prisma,
          );
        }

        // Redeem referral if applicable
        await this.referralService.tryRedeem({ newPatientId: profile.id });
      });
    } catch (e) {
      console.error(e);
      throw e;
    }
  }
}
