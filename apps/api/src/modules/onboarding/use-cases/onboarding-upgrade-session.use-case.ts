import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { CognitoService } from '@modules/auth/cognito.service';
import { JwtService } from '@modules/auth/jwt.service';
import { OnboardingService } from '@modules/onboarding/onboarding.service';
import { OnboardingCookieService } from '@modules/onboarding/services/onboarding-cookie.service';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states';
import { OnboardingAuthTokens } from '@modules/onboarding/types/cookie.types';
import { PrismaService } from '@modules/prisma/prisma.service';
import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';

import { UpgradeSessionDto } from '../dto/upgrade-session.dto';

@Injectable()
export class OnboardingUpgradeSessionUseCase {
  constructor(
    private readonly jwtService: JwtService,
    private readonly cognitoService: CognitoService,
    private readonly prismaService: PrismaService,
    private readonly patientPersistence: PatientPersistence,
    private readonly onboardingStateService: OnboardingStateService,
    private readonly onboardingCookieService: OnboardingCookieService,
    @Inject(forwardRef(() => OnboardingService))
    private readonly onboardingService: OnboardingService,
  ) {}

  async execute(data: UpgradeSessionDto) {
    let accessToken = data.accessToken;
    let refreshToken = data.refreshToken;
    let userId: string;
    let role: string;

    // Step 1: Verify access token
    try {
      const decodedToken = await this.jwtService.verifyToken(accessToken);
      userId = decodedToken.sub;
      role = decodedToken['cognito:groups']?.[0];

      if (!userId || !role) {
        throw new UnauthorizedException('Invalid token payload');
      }

      if (role !== 'Patient') {
        throw new ForbiddenException('Only patient tokens can be upgraded');
      }
    } catch (tokenError) {
      // Step 2: If token expired, try to refresh
      try {
        // We need to assume role is Patient since we can't decode expired token reliably
        // The refresh will fail if the refresh token is invalid anyway
        const session = await this.cognitoService.refresh(
          refreshToken,
          'Patient',
        );

        // Extract new tokens
        accessToken = session.getAccessToken().getJwtToken();
        refreshToken = session.getRefreshToken().getToken();

        // Verify the new access token
        const decodedToken = await this.jwtService.verifyToken(accessToken);
        userId = decodedToken.sub;
        role = decodedToken['cognito:groups']?.[0];

        if (!userId || !role || role !== 'Patient') {
          throw new UnauthorizedException(
            'Invalid token payload after refresh',
          );
        }
      } catch (refreshError) {
        throw new UnauthorizedException('Token refresh failed');
      }
    }

    // Step 3: Fetch patient from database
    const user = await this.prismaService.user.findFirst({
      where: { id: userId, type: 'patient', deletedAt: null },
      include: { patient: { include: { state: true } } },
    });

    if (!user || !user.patient) {
      throw new NotFoundException('Patient not found');
    }

    const patient = user.patient;

    // Step 4: Validate patient is in onboarding phase
    const onboardingStatuses = ['onboardingPending', 'onboardingRejected'];
    if (!onboardingStatuses.includes(patient.status)) {
      throw new BadRequestException('Patient is not in onboarding phase');
    }

    // Step 5: Create auth tokens for cookie
    const authTokens: OnboardingAuthTokens = {
      accessToken,
      refreshToken,
      role,
      patientId: patient.id,
    };

    // Step 6: Get or restore onboarding state
    if (!patient.onboardingState) {
      // New onboarding patient - initialize state
      const { snapshot, response, cookie } =
        await this.onboardingService.ensureActorFromCookieOrInitCurrent();

      const updatedCookie = {
        ...cookie,
        authTokens,
      };

      // Set the cookie for the response
      this.onboardingCookieService.setCookie(updatedCookie);

      return await this.onboardingService.prepareClientResponse(
        response,
        snapshot,
      );
    }

    // Existing onboarding patient - restore from database state
    const version = patient.onboardingVersion as OnboardingVersion;
    const savedSnapshot = patient.onboardingState;

    // Get onboarding state (auto-upgrades if version is deprecated)
    const {
      state: restoredResponse,
      version: actualVersion,
      snapshot: restoredSnapshot,
    } = await this.onboardingStateService.getOrUpgradeOnboardingState(
      version,
      savedSnapshot,
      patient.id,
    );

    // Step 7: Create cookie with restored state and auth tokens
    const cookie = {
      version: actualVersion,
      startedAt: patient.createdAt?.toISOString() || new Date().toISOString(),
      currentState: this.getStateString(restoredSnapshot.value),
      authTokens,
    };

    // Step 8: Set cookie and return response
    this.onboardingCookieService.setCookie(cookie);

    return await this.onboardingService.prepareClientResponse(
      restoredResponse,
      restoredSnapshot,
    );
  }

  /**
   * Helper to convert XState value to string representation
   * Copied from PatientSignInUseCase
   */
  private getStateString(value: any): string {
    if (typeof value === 'string') return value;
    if (typeof value === 'object' && value !== null) {
      const keys = Object.keys(value as Record<string, any>);
      if (keys.length === 1) {
        const key = keys[0];
        const nestedValue = (value as any)[key];
        if (typeof nestedValue === 'object') {
          return `${key}.${this.getStateString(nestedValue)}`;
        }
        return `${key}.${nestedValue}`;
      }
    }
    return String(value);
  }
}
