import {
  PrismaService,
  PrismaTransactionalClient,
} from '@modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { medicalNecessitySetBy } from '@prisma/client';

import type { ObjectiveId } from '@willow/utils/medical-necessity';
import { MEDICAL_NECESSITY_MAPPINGS } from '@willow/utils';

@Injectable()
export class MedicalNecessityService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Gets the display text for a medical necessity key
   */
  getMedicalNecessityText(key: string): string | undefined {
    return MEDICAL_NECESSITY_MAPPINGS[key as ObjectiveId]?.text;
  }

  /**
   * Gets the highest priority medical necessity from a list
   * Lower priority number = higher priority (1 is highest)
   */
  getHighestPriorityNecessity(
    necessities: { necessity: string }[],
  ): string | null {
    if (!necessities || necessities.length === 0) {
      return null;
    }

    // Sort by priority and return the first one
    const sorted = necessities
      .filter((n) => MEDICAL_NECESSITY_MAPPINGS[n.necessity as ObjectiveId])
      .sort((a, b) => {
        const priorityA =
          MEDICAL_NECESSITY_MAPPINGS[a.necessity as ObjectiveId]?.priority ||
          999;
        const priorityB =
          MEDICAL_NECESSITY_MAPPINGS[b.necessity as ObjectiveId]?.priority ||
          999;
        return priorityA - priorityB;
      });

    return sorted.length > 0 ? sorted[0].necessity : null;
  }

  /**
   * Creates medical necessity records for a patient based on their objectives
   */
  async createPatientMedicalNecessities(
    patientId: string,
    objectives: string[],
    bmi?: number,
    client?: PrismaTransactionalClient,
  ): Promise<void> {
    const prismaClient = client || this.prisma;

    const medicalNecessities = objectives
      .filter((objective): objective is ObjectiveId =>
        Object.hasOwnProperty.call(MEDICAL_NECESSITY_MAPPINGS, objective),
      )
      .map(
        (objective) =>
          ({
            patientId,
            necessity: objective,
            setBy: 'patient' as const,
          }) as {
            patientId: string;
            necessity: ObjectiveId;
            setBy: medicalNecessitySetBy;
          },
      );

    if (bmi && bmi <= 27) {
      medicalNecessities.push({
        patientId,
        necessity: 'bmiUnder27',
        setBy: 'system' as const,
      });
    }

    if (medicalNecessities.length > 0) {
      await prismaClient.medicalNecessity.createMany({
        data: medicalNecessities,
      });
    }
  }

  /**
   * Updates medical necessity records for a patient (only replaces doctor-set ones)
   */
  async updatePatientMedicalNecessities(
    patientId: string,
    necessityKeys: string[],
    client?: PrismaTransactionalClient,
  ): Promise<void> {
    const prismaClient = client || this.prisma;

    // Validate that all keys are valid
    const validKeys = necessityKeys.filter((key): key is ObjectiveId =>
      Object.hasOwnProperty.call(MEDICAL_NECESSITY_MAPPINGS, key),
    );

    // Only delete doctor-set medical necessities, preserve patient and system ones
    await prismaClient.medicalNecessity.deleteMany({
      where: { patientId, setBy: 'doctor' },
    });

    // Create new doctor-set medical necessities
    if (validKeys.length > 0) {
      const medicalNecessities = validKeys.map((necessity) => ({
        patientId,
        necessity,
        setBy: 'doctor' as const,
      }));

      await prismaClient.medicalNecessity.createMany({
        data: medicalNecessities,
      });
    }
  }
}
