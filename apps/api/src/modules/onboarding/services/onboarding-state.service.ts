import type { OnboardingContextMap } from '@modules/onboarding/states';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { OnboardingVersionManager } from '@modules/onboarding/helpers/onboarding-version-manager';
import {
  AnyOnboardingInput,
  ONBOARDING_MACHINE_REGISTRY,
  OnboardingVersion,
} from '@modules/onboarding/states';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { HttpException, Injectable } from '@nestjs/common';
import { createActor, fromPromise } from 'xstate';

// Generic type to get context type from version
export type OnboardingContextForVersion<T extends OnboardingVersion> =
  OnboardingContextMap[T & keyof OnboardingContextMap];

// Content configuration that can be defined in state machine metadata
export interface OnboardingContentConfig {
  title?: string;
  subtitle?: string | string[];
  description?: string;
  buttonText?: string;
}

// Generic return type for getCurrentOnboardingState
export interface OnboardingStateResult<T extends OnboardingVersion> {
  state: any;
  context: OnboardingContextForVersion<T>;
  events: string[];
  stepName: string;
  percentage: number;
  content?: OnboardingContentConfig;
  component: string | undefined;
}

// Generic OnboardingActor type that preserves version information
export interface OnboardingActorForVersion<T extends OnboardingVersion> {
  // OnboardingSnapshot represents the serialized state stored in database
  // Using 'any' for now since our simplified actor interface returns a different shape
  // than the full XState snapshot, but persistence layer expects the full snapshot
  getSnapshot(): {
    context: OnboardingContextForVersion<T>;
    value: any;
    status: string;
    can: (event: any) => boolean;
    machine: any;
  };
  getPersistedSnapshot(): unknown;
  send: (event: any) => void;
  start?: () => OnboardingActorForVersion<T>;
}

// OnboardingSnapshot represents the serialized state stored in database
// Using 'any' for now since our simplified actor interface returns a different shape
// than the full XState snapshot, but persistence layer expects the full snapshot
export type OnboardingSnapshot = any;

export type OnboardingProfile = Awaited<
  ReturnType<OnboardingStateService['getCurrentOnboardingState']>
>;

@Injectable()
export class OnboardingStateService {
  private readonly logger: LoggerService;

  private readonly SECTION_NAMES = {
    preSignup: 'Account Creation',
    questionnaire: 'Health Questionnaire',
    selectTreatmentType: 'Select Your Medication',
    selectTreatment: 'Select Your Medication',
    identityVerification: 'Identity Verification',
    ssnCheck: 'Identity Verification',
    ssnSuccess: 'Identity Verification',
    info: 'Virtual Doctor Visit',
    uploadIdPhoto: 'Upload Your Photos',
    uploadFacePhoto: 'Upload Your Photos',
    shippingInfo: 'Checkout',
    paymentInfo: 'Checkout',
    reviewOrder: 'Checkout',
    confirmPayment: 'Checkout',
    processingPayment: 'Checkout',
    onboarded: 'Completed',
  };

  constructor(
    private readonly patientPersistence: PatientPersistence,
    private readonly versionManager: OnboardingVersionManager,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(OnboardingStateService.name);
  }
  /**
   * Reset deprecated onboarding to fresh state with latest version
   * This method handles the complete reset process:
   * 1. Fetches patient data from database (source of truth)
   * 2. Creates fresh initial state with latest version (no snapshot restoration)
   * 3. Injects preserved data via reset event
   * 4. Updates patient record in database
   * 5. Returns the new onboarding state
   */
  async resetDeprecatedOnboarding<T extends OnboardingVersion>(
    patientId: string,
    currentVersion: OnboardingVersion,
    existingSnapshot?: any,
  ): Promise<OnboardingStateResult<T>> {
    console.warn(
      `Resetting deprecated onboarding version ${currentVersion} for patient ${patientId} to questionnaire.age`,
    );

    // Get the latest version
    const latestVersion =
      await this.versionManager.getDefaultOnboardingVersion();

    // Fetch patient from database to extract preserved fields (source of truth)
    const patient =
      await this.patientPersistence.getOnboardingProfile(patientId);

    // Extract preserved fields from database record
    const preservedData: any = {};

    // Map user fields
    preservedData.email = patient.user.email;
    preservedData.firstName = patient.user.firstName;
    preservedData.lastName = patient.user.lastName;
    preservedData.phone = patient.user.phone;

    // Map patient fields
    preservedData.pharmacyId = patient.pharmacyId;
    preservedData.getPromotionsSMS = patient.getPromotionsSMS;

    preservedData.state = patient.state.code;

    // Extract context-only fields from snapshot if available
    // These fields don't exist in the database but may be in the context
    if (existingSnapshot?.context) {
      const contextOnlyFields = ['promoCoupon', 'referralCode', 'discount'];
      contextOnlyFields.forEach((field) => {
        if (existingSnapshot.context[field] !== undefined) {
          preservedData[field] = existingSnapshot.context[field];
        }
      });
    }

    // Create fresh actor (no snapshot = no state restoration risk)
    const actor = await this.getCurrentOnboardingActor(
      latestVersion,
      undefined,
    );

    // Send reset event with preserved data from database
    actor.send({ type: 'reset', data: preservedData });

    // Get the new snapshot after reset
    const newSnapshot = actor.getSnapshot();

    // Recover from error state before persisting
    const recoveredSnapshot = this.recoverSnapshot(newSnapshot);

    // Update patient record with new version and state
    await this.patientPersistence.updateOnboarding(
      patientId,
      recoveredSnapshot,
      {
        onboardingVersion: latestVersion,
      },
    );

    // Return the questionnaire.age onboarding state
    return this.getCurrentOnboardingState(latestVersion as T, newSnapshot);
  }

  async getCurrentOnboardingActor<T extends OnboardingVersion>(
    version: T,
    snapshot: AnyOnboardingSnapshot,
    actors?: any,
  ): Promise<OnboardingActorForVersion<T>> {
    // Validate version exists in registry
    if (!(version in ONBOARDING_MACHINE_REGISTRY)) {
      const availableVersions = Object.keys(ONBOARDING_MACHINE_REGISTRY).join(
        ', ',
      );
      throw new Error(
        `Unknown onboarding version: ${version}. Available versions: ${availableVersions}`,
      );
    }

    return (await this.buildVersionedOnboardingMachine(
      version,
      snapshot,
      actors,
    )) as OnboardingActorForVersion<T>;
  }

  /**
   * Get onboarding state for a patient, automatically upgrading deprecated versions
   * @returns The onboarding state, actual version used, and snapshot
   */
  async getOrUpgradeOnboardingState<T extends OnboardingVersion>(
    version: OnboardingVersion,
    snapshot: AnyOnboardingSnapshot,
    patientId: string,
  ): Promise<{
    state: OnboardingStateResult<T>;
    version: OnboardingVersion;
    snapshot: any;
  }> {
    // Check if version is deprecated
    if (this.versionManager.isVersionDeprecated(version)) {
      // Version is deprecated - reset to latest
      const state = await this.resetDeprecatedOnboarding<T>(
        patientId,
        version,
        snapshot,
      );

      const latestVersion =
        await this.versionManager.getDefaultOnboardingVersion();

      // Create snapshot-like object for response
      const upgradedSnapshot = {
        value: state.state,
        context: state.context,
      };

      return {
        state,
        version: latestVersion,
        snapshot: upgradedSnapshot,
      };
    }

    // Not deprecated - proceed normally
    const state = await this.getCurrentOnboardingState(
      version as T,
      snapshot,
      patientId,
    );

    return {
      state,
      version,
      snapshot,
    };
  }

  private getStatePath(stateValue: any): string[] {
    try {
      this.logger.debug(`Getting state path for state value`, { stateValue });

      if (typeof stateValue === 'string') {
        return [stateValue];
      }
      // Handle nested state objects like { questionnaire: 'age' }
      const keys = Object.keys(stateValue);
      if (keys.length === 1) {
        return [keys[0], ...this.getStatePath(stateValue[keys[0]])];
      }
      return [];
    } catch (error) {
      this.logger.error(
        error as Error,
        { message: `Error getting state path`, stateValue },
        'GET_STATE_PATH_ERROR',
      );
      return [];
    }
  }

  private getStateMetadata(snapshot: any): {
    stepName: string;
    percentage: number;
    content?: OnboardingContentConfig;
    component: string | undefined;
  } {
    const stateValue = snapshot.value;

    // Handle final state
    if (stateValue === 'onboarded') {
      return {
        stepName: this.SECTION_NAMES['onboarded'],
        percentage: 100,
        component: undefined,
      };
    }

    // Get state path (e.g., ['questionnaire', 'age'] or ['preSignup', 'stateSelection'])
    const statePath = this.getStatePath(stateValue);
    const rootState = statePath[0];

    // Navigate through the state configuration to find the current state's metadata
    let absoluteStep = 0;
    let name = '';
    let content: OnboardingContentConfig | undefined;
    let component: string | undefined = undefined;
    let stateConfig = snapshot?.machine?.config?.states;

    for (let i = 0; i < statePath.length; i++) {
      const stateName = statePath[i];
      if (stateConfig && stateConfig[stateName]) {
        // Get step, name, and content from the current state's meta if they exist
        if (stateConfig[stateName]?.meta) {
          absoluteStep = stateConfig[stateName].meta.step || absoluteStep;
          name = stateConfig[stateName].meta.name || name;

          component = stateConfig[stateName].meta.component || component;

          // Extract content from meta if it exists
          if (stateConfig[stateName].meta.content) {
            content = {
              title: stateConfig[stateName].meta.content.title,
              subtitle: stateConfig[stateName].meta.content.subtitle,
              description: stateConfig[stateName].meta.content.description,
              buttonText: stateConfig[stateName].meta.content.buttonText,
            };

            // Remove undefined properties
            Object.keys(content).forEach((key) => {
              if (
                content![key as keyof OnboardingContentConfig] === undefined
              ) {
                delete content![key as keyof OnboardingContentConfig];
              }
            });

            // If no content properties remain, set to undefined
            if (Object.keys(content).length === 0) {
              content = undefined;
            }
          }
        }

        // Navigate to nested states for the next iteration
        if (i < statePath.length - 1 && stateConfig[stateName].states) {
          stateConfig = stateConfig[stateName].states;
        }
      }
    }

    // If no name was found in meta, fall back to SECTION_NAMES
    if (!name) {
      name = this.SECTION_NAMES[rootState] || '';
    }

    // Calculate timeline-based percentage
    let percentage: number;

    if (rootState === 'preSignup') {
      // Timeline 1: Pre-signup (4 steps)
      const currentStep = Math.min(absoluteStep, 4);
      percentage = Math.round((currentStep / 4) * 100);
    } else {
      // Timeline 2: Main onboarding (questionnaire + post-questionnaire = 24 steps)
      let currentStep: number;

      if (rootState === 'questionnaire') {
        // Questionnaire steps: absolute steps 5-19 map to timeline steps 1-15
        currentStep = absoluteStep - 4; // Step 5 becomes step 1, step 19 becomes step 15
      } else {
        // Post-questionnaire states
        const postQuestionnaireStepMap: Record<string, number> = {
          selectTreatmentType: 16,
          selectTreatment: 17,
          info: 18,
          uploadIDPhoto: 19,
          uploadFacePhoto: 20,
          visitCompletion: 21,
          summary: 22,
          shipping: 23,
          payment: 24,
        };

        currentStep = postQuestionnaireStepMap[rootState] || 16;
      }

      percentage = Math.round((currentStep / 24) * 100);
    }

    return {
      stepName: name,
      percentage,
      content,
      component,
    };
  }

  // Method overloads for getCurrentOnboardingState
  async getCurrentOnboardingState<T extends OnboardingVersion>(
    version: T,
    snapshot: any,
  ): Promise<OnboardingStateResult<T>>;
  async getCurrentOnboardingState<T extends OnboardingVersion>(
    version: T,
    snapshot: any,
    patientId?: string,
  ): Promise<OnboardingStateResult<T>>;
  async getCurrentOnboardingState<T extends OnboardingVersion>(
    version: T,
    snapshot: any,
    patientId?: string,
  ): Promise<OnboardingStateResult<T>> {
    // Check for deprecated version and reset if needed
    if (patientId && this.versionManager.shouldResetOnboarding(version)) {
      console.warn(
        `Deprecated onboarding version ${version} detected for patient ${patientId}, resetting to latest version`,
      );
      return this.resetDeprecatedOnboarding<T>(patientId, version, snapshot);
    }
    // Build an actor with no-op runtime actors to avoid invoking real services
    // when we just need to inspect current state/metadata.
    const actor = await this.getCurrentOnboardingActor(
      version,
      snapshot,
      this.getNoopRuntimeActors(),
    );

    const onboardingSnapshot = actor.getSnapshot();
    const { stepName, percentage, content, component } =
      this.getStateMetadata(onboardingSnapshot);

    // Get available events from the current state without executing guards
    const stateValue = onboardingSnapshot.value;
    const statePath = this.getStatePath(stateValue); // Use existing helper method

    // Navigate the machine configuration to find the current state's events
    let events: string[] = [];
    try {
      // Access the machine config through the actor
      const machineConfig = (actor as any).logic?.config;

      if (machineConfig?.states) {
        // Navigate to the current state node
        let currentStateConfig = machineConfig.states;

        for (const key of statePath) {
          if (currentStateConfig[key]) {
            // Get events from the current level
            const stateNode = currentStateConfig[key];
            if (stateNode?.on) {
              const stateEvents = Object.keys(stateNode.on).filter(
                (event) => event !== '',
              );
              events.push(...stateEvents);
            }

            // Navigate deeper if there are nested states
            currentStateConfig = currentStateConfig[key].states || {};
          }
        }
      }
    } catch (error) {
      console.warn('Could not extract events from machine config:', error);
      events = [];
    }

    events = [...new Set(events)];

    return {
      state: onboardingSnapshot.value,
      context: onboardingSnapshot.context,
      events,
      stepName,
      percentage,
      content,
      component,
    };
  }

  // Provide no-op implementations for all actors so we can safely start
  // the machine at any state without wiring real services.
  private getNoopRuntimeActors() {
    return {
      checkStateEnabled: fromPromise(async ({ input }) => ({
        state: (input as any)?.state ?? '',
        enabled: true,
      })),
      createUser: fromPromise(async () => ({ success: true })),
      addToWaitingList: fromPromise(async () => ({ success: true })),
      validateName: fromPromise(async () => ({ valid: true })),
      verifySsn: fromPromise(async () => ({ success: true })),
      validateTreatmentType: fromPromise(async () => ({ success: true })),
      validateTreatment: fromPromise(async () => ({ success: true })),
      validateShipping: fromPromise(async () => ({ success: true })),
      validateAndSaveShipping: fromPromise(async () => ({ success: true })),
      setupPaymentIntent: fromPromise(async () => ({ clientSecret: '' })),
    };
  }

  /**
   * Recover snapshot from error state before persisting
   * We should never persist error states to the database
   */
  private recoverSnapshot(
    snapshot: AnyOnboardingSnapshot,
  ): AnyOnboardingSnapshot {
    if (snapshot.status === 'error') {
      console.warn('Recovering onboarding snapshot from error state');
      return {
        ...snapshot,
        status: 'active',
        error: undefined,
      };
    }
    return snapshot;
  }

  private async buildVersionedOnboardingMachine(
    version: OnboardingVersion,
    snapshot?: AnyOnboardingSnapshot,
    actors?: any,
  ) {
    const versionedMachine = ONBOARDING_MACHINE_REGISTRY[version];

    if (!versionedMachine) {
      throw new Error(`Onboarding version ${version} not found`);
    }

    try {
      const machine = actors
        ? versionedMachine.provide({ actors })
        : versionedMachine;

      const actor = snapshot
        ? createActor(machine, { snapshot: machine.resolveState(snapshot) })
        : createActor(machine, {
            input: {
              version,
              existingData: {},
            } as AnyOnboardingInput,
          });

      return actor.start();
    } catch (e) {
      this.logger.error(
        e,
        {
          version,
          snapshot,
        },
        'ERROR_BUILDING_ONBOARDING_MACHINE',
      );
      throw new HttpException(e.message, 500);
    }
  }
}
