import { PatientPersistence } from '@/adapters/persistence/database/patient.persistence';
import { Injectable } from '@nestjs/common';

import type { OnboardingVersion } from '../states';
import type {
  AnyOnboardingSnapshot,
  OnboardingCookie,
} from '../types/cookie.types';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';

@Injectable()
export class OnboardingPersistence {
  constructor(
    private readonly cookieService: OnboardingCookieService,
    private readonly patientPersistence: PatientPersistence,
  ) {}

  /**
   * Utility to convert state value to string representation
   */
  private getStateString(value: any): string {
    if (typeof value === 'string') return value;
    if (typeof value === 'object' && value !== null) {
      const keys = Object.keys(value as Record<string, any>);
      if (keys.length === 1) {
        const key = keys[0];
        const nestedValue = (value as any)[key];
        if (typeof nestedValue === 'object') {
          return `${key}.${this.getStateString(nestedValue)}`;
        }
        return `${key}.${nestedValue}`;
      }
    }
    return String(value);
  }

  /**
   * Clean snapshot for database persistence - removes auth tokens and sensitive data
   */
  private cleanSnapshotForDb(
    snapshot: AnyOnboardingSnapshot,
  ): AnyOnboardingSnapshot {
    if (snapshot?.context) {
      const { authData, validationError, ...cleanContext } = snapshot.context;
      return {
        ...snapshot,
        context: cleanContext,
      };
    }
    return snapshot;
  }

  /**
   * Recover snapshot from error state before persisting
   * We should never persist error states to the database
   */
  private recoverSnapshot(
    snapshot: AnyOnboardingSnapshot,
  ): AnyOnboardingSnapshot {
    if (snapshot.status === 'error') {
      console.warn('Recovering onboarding snapshot from error state');
      return {
        ...snapshot,
        status: 'active',
        error: undefined,
      };
    }
    return snapshot;
  }

  /**
   * Persist cookie with updated state and snapshot
   */
  persistCookie(cookie: OnboardingCookie, actor: any): OnboardingCookie {
    const snapshot = actor.getSnapshot();
    const persistedSnapshot = (actor as any).getPersistedSnapshot();

    // Extract auth data from context if present
    const authData = (snapshot.context as any)?.authData;

    // Clean the persisted snapshot before storing in cookie - never store auth tokens
    const cleanedSnapshot = this.cleanSnapshotForDb(persistedSnapshot);

    const updatedCookie: OnboardingCookie = {
      ...cookie,
      currentState: this.getStateString(snapshot.value),
    };

    // Check for auth tokens from both new context and existing cookie
    // @todo eventually remove the getStarted check, it's a legacy check
    const hasNewAuthTokens =
      authData?.accessToken &&
      authData?.refreshToken &&
      authData?.patientId &&
      !authData?.getStarted;
    const hasExistingAuthTokens = cookie?.authTokens?.patientId;

    // Determine if this user is authenticated (either new tokens or existing tokens)
    const isAuthenticated = hasNewAuthTokens || hasExistingAuthTokens;

    // Add new auth tokens to cookie if present (extracted from context)
    if (hasNewAuthTokens) {
      (updatedCookie as any).authTokens = {
        accessToken: authData.accessToken,
        refreshToken: authData.refreshToken,
        role: authData.role || 'Patient',
        patientId: authData.patientId,
      };
    } else if (hasExistingAuthTokens) {
      // Preserve existing auth tokens if no new ones
      (updatedCookie as any).authTokens = cookie.authTokens;
    }

    // Only include stateSnapshot in cookie if user is NOT authenticated (pre-signup phase)
    // For authenticated users, snapshot will be fetched from database
    if (!isAuthenticated) {
      (updatedCookie as any).stateSnapshot =
        cleanedSnapshot as AnyOnboardingSnapshot;
    }

    this.cookieService.setCookie(updatedCookie);
    return updatedCookie;
  }

  /**
   * Persist to database if patient exists and is past pre-signup
   */
  async persistToDatabase(
    cookie: OnboardingCookie,
    actor: any,
    version: OnboardingVersion,
  ): Promise<void> {
    const patientId = cookie.authTokens?.patientId;
    const isPreSignup = cookie.currentState?.startsWith('preSignup');

    if (patientId && !isPreSignup) {
      const rawSnapshot = (actor as any).getPersistedSnapshot();
      const recoveredSnapshot = this.recoverSnapshot(rawSnapshot);
      const snapshotForDb = this.cleanSnapshotForDb(recoveredSnapshot);

      await this.patientPersistence.updateOnboarding(patientId, snapshotForDb, {
        onboardingVersion: version,
      });
    }
  }

  /**
   * Persist only snapshot to database (used for discount attachment)
   */
  async persistSnapshotOnly(
    patientId: string,
    snapshot: AnyOnboardingSnapshot,
    version: OnboardingVersion,
  ): Promise<void> {
    const recoveredSnapshot = this.recoverSnapshot(snapshot);
    const cleanedSnapshot = this.cleanSnapshotForDb(recoveredSnapshot);
    await this.patientPersistence.updateOnboarding(patientId, cleanedSnapshot, {
      onboardingVersion: version,
    });
  }

  /**
   * Full persistence - both cookie and database
   */
  async persistAll(
    cookie: OnboardingCookie,
    actor: any,
    version: OnboardingVersion,
  ): Promise<OnboardingCookie> {
    const updatedCookie = this.persistCookie(cookie, actor);
    await this.persistToDatabase(updatedCookie, actor, version);
    return updatedCookie;
  }
}
