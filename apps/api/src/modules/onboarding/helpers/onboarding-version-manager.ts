import { SettingService } from '@modules/setting/setting.service';
import { Injectable } from '@nestjs/common';

import type { OnboardingFeatures } from '../states/onboarding-versions.config';
import type { OnboardingVersion } from '../states/types/onboarding-generic.types';
import { ONBOARDING_VERSIONS_CONFIG } from '../states/onboarding-versions.config';

@Injectable()
export class OnboardingVersionManager {
  constructor(private readonly settingService: SettingService) {}

  /**
   * Get the default onboarding version for new patients
   * Checks for settings override (supports major version groups) before falling back to config-based selection
   */
  async getDefaultOnboardingVersion(): Promise<OnboardingVersion> {
    // Check for settings override
    const overrideValue =
      await this.settingService.fetchDefaultValue('onboardingVersion');

    if (overrideValue) {
      // Check if it's a group ID (major version like "v4")
      const groupVersions = this.getVersionsInGroup(overrideValue);
      if (groupVersions.length > 0) {
        return this.selectVersionFromGroup(overrideValue);
      }

      // Check if it's a specific version
      const validVersions = this.getAllVersions();
      if (validVersions.includes(overrideValue as OnboardingVersion)) {
        console.log(`Using specific version override: ${overrideValue}`);
        return overrideValue as OnboardingVersion;
      }
    }

    // Fall back to config-based selection
    return this.getDefaultVersion();
  }

  /**
   * Get configuration for a specific version
   */
  getConfig<T extends OnboardingVersion>(version: T) {
    return ONBOARDING_VERSIONS_CONFIG.find(
      (config) => config.version === version,
    )!;
  }

  /**
   * Get all versions sorted by priority
   */
  getAllVersions(): OnboardingVersion[] {
    return ONBOARDING_VERSIONS_CONFIG.map((config) => config.version);
  }

  /**
   * Get the default version from config (synchronous, no settings override)
   * Handles A/B test selection when multiple default versions exist
   * For async version with settings override, use getDefaultOnboardingVersion()
   */
  getDefaultVersion(): OnboardingVersion {
    const defaultConfigs = ONBOARDING_VERSIONS_CONFIG.filter(
      (config) => config.isDefault,
    );

    if (defaultConfigs.length === 0) {
      throw new Error('No default version configured');
    }

    // Single default (backward compatible)
    if (defaultConfigs.length === 1) {
      return defaultConfigs[0].version;
    }

    // Multiple defaults - check if they form an A/B test group
    const groupIds = new Set(
      defaultConfigs.map((config) => config.groupId).filter(Boolean),
    );

    if (groupIds.size === 1) {
      // All defaults belong to same group - select based on weights
      const groupId = [...groupIds][0]!;
      return this.selectVersionFromGroup(groupId);
    }

    // Multiple ungrouped defaults or mixed groups - fallback to priority
    const highestPriority = Math.max(
      ...defaultConfigs.map((config) => config.priority),
    );
    const priorityDefaults = defaultConfigs.filter(
      (config) => config.priority === highestPriority,
    );

    if (priorityDefaults.length === 1) {
      return priorityDefaults[0].version;
    }

    // Still multiple - use group default or first
    const groupDefault = priorityDefaults.find((config) => config.groupDefault);
    return groupDefault?.version ?? priorityDefaults[0].version;
  }

  /**
   * Check if a version is deprecated
   */
  isVersionDeprecated(version: OnboardingVersion): boolean {
    return this.getConfig(version).deprecated;
  }

  /**
   * Check if onboarding should be reset due to deprecation
   */
  shouldResetOnboarding(version: OnboardingVersion): boolean {
    return this.isVersionDeprecated(version);
  }

  /**
   * Check if a version has a specific capability
   * @param version The version to check
   * @param capability The capability to check for
   * @returns True if the version has the capability
   * @example hasCapability('v3', 'hasBMICalculation') // true
   */
  hasCapability<K extends keyof OnboardingFeatures>(
    version: OnboardingVersion,
    capability: K,
  ): boolean {
    return this.getConfig(version).features[capability] === true;
  }

  /**
   * Get all versions in a specific A/B test group
   */
  getVersionsInGroup(groupId: string): OnboardingVersion[] {
    return ONBOARDING_VERSIONS_CONFIG.filter(
      (config) => config.groupId === groupId,
    ).map((config) => config.version);
  }

  /**
   * Randomly select a version from an A/B test group based on weights
   */
  selectVersionFromGroup(groupId: string): OnboardingVersion {
    const groupVersions = ONBOARDING_VERSIONS_CONFIG.filter(
      (config) => config.groupId === groupId,
    );

    if (groupVersions.length === 0) {
      throw new Error(`No versions found in group: ${groupId}`);
    }

    if (groupVersions.length === 1) {
      return groupVersions[0].version;
    }

    // Calculate total weight
    const totalWeight = groupVersions.reduce(
      (sum, config) => sum + (config.weight ?? 1 / groupVersions.length),
      0,
    );

    // Weighted random selection with cumulative thresholds
    const random = Math.random() * totalWeight;
    let cumulativeWeight = 0;

    for (const config of groupVersions) {
      const weight = config.weight ?? 1 / groupVersions.length;
      cumulativeWeight += weight;
      if (random < cumulativeWeight) {
        return config.version;
      }
    }

    // Fallback to group default or first
    const fallback = groupVersions.find((config) => config.groupDefault);
    return fallback?.version ?? groupVersions[0].version;
  }
}
