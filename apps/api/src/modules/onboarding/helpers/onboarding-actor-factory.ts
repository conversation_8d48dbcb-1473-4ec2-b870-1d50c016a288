import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { ValidateNameActorService } from '@modules/onboarding/actors/validate-name.actor';
import {
  LoggerFactory,
  LoggerService,
} from '@modules/shared/logger/logger.service';
import { HttpException, Injectable } from '@nestjs/common';
import { createActor, fromPromise } from 'xstate';

import type {
  AnyOnboardingSnapshot,
  OnboardingCookie,
} from '../types/cookie.types';
import { AddToWaitingListActorService } from '../actors/add-to-waiting-list-actor.service';
import { CheckStateEnabledActorService } from '../actors/check-state-enabled-actor.service';
import { CreateUserActorService } from '../actors/create-user-actor.service';
import { ValidateShippingActorService } from '../actors/validate-shipping-actor.service';
import { ValidateTreatmentActorService } from '../actors/validate-treatment-actor.service';
import { ValidateTreatmentTypeActorService } from '../actors/validate-treatment-type-actor.service';
import { VerifySsnActorService } from '../actors/verify-ssn-actor.service';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';
import {
  AnyOnboardingInput,
  ONBOARDING_MACHINE_REGISTRY,
  OnboardingVersion,
} from '../states';
import { OnboardingVersionManager } from './onboarding-version-manager';

@Injectable()
export class OnboardingActorFactory {
  private readonly logger: LoggerService;
  constructor(
    private readonly checkStateEnabledActorService: CheckStateEnabledActorService,
    private readonly addToWaitingListActorService: AddToWaitingListActorService,
    private readonly createUserActorService: CreateUserActorService,
    private readonly validateTreatmentActorService: ValidateTreatmentActorService,
    private readonly validateTreatmentTypeActorService: ValidateTreatmentTypeActorService,
    private readonly verifySsnActorService: VerifySsnActorService,
    private readonly validateShippingActorService: ValidateShippingActorService,
    private readonly validateNameActorService: ValidateNameActorService,
    private readonly cookieService: OnboardingCookieService,
    private readonly versionManager: OnboardingVersionManager,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(PatientPersistence.name);
  }

  /**
   * Creates runtime actors that perform actual API calls and operations
   */
  createRuntimeActors(cookie?: OnboardingCookie) {
    return {
      checkStateEnabled: this.checkStateEnabledActorService.createActor(),
      validateName: this.validateNameActorService.createActor(),
      addToWaitingList: this.addToWaitingListActorService.createActor(),
      createUser: this.createUserActorService.createActor(cookie),
      verifySsn: this.verifySsnActorService.createActor(),
      validateTreatmentType: this.validateTreatmentTypeActorService.createActor(
        this.cookieService,
        cookie,
      ),
      validateTreatment: this.validateTreatmentActorService.createActor(),
      validateShipping: this.validateShippingActorService.createActor(),
    };
  }

  /**
   * Creates no-op actors for state inspection without side effects
   */
  createNoOpActors() {
    return {
      checkStateEnabled: fromPromise(async ({ input }) => ({
        state: (input as any)?.state ?? '',
        enabled: true,
      })),
      createUser: fromPromise(async () => ({ success: true })),
      addToWaitingList: fromPromise(async () => ({ success: true })),
      validateName: fromPromise(async () => ({ valid: true })),
      verifySsn: fromPromise(async () => ({ success: true })),
      validateTreatmentType: fromPromise(async () => ({ success: true })),
      validateTreatment: fromPromise(async () => ({ success: true })),
      validateShipping: fromPromise(async () => ({ success: true })),
      validateAndSaveShipping: fromPromise(async () => ({ success: true })),
      setupPaymentIntent: fromPromise(async () => ({ clientSecret: '' })),
    };
  }

  /**
   * Creates an onboarding actor from a version and optional snapshot
   */
  async createOnboardingActor(
    version: OnboardingVersion,
    snapshot?: AnyOnboardingSnapshot,
    useRuntimeActors = false,
    cookie?: OnboardingCookie,
  ) {
    // Check if the requested version is deprecated
    if (this.versionManager.isVersionDeprecated(version)) {
      console.warn(
        `Deprecated onboarding version ${version} detected, switching to latest version`,
      );

      // Get the latest version
      const defaultVersion =
        await this.versionManager.getDefaultOnboardingVersion();

      // Create actor with latest version
      const latestActor = await this.createOnboardingActor(
        defaultVersion,
        undefined, // Don't pass snapshot to avoid old state
        useRuntimeActors,
        cookie,
      );

      // If we had an existing snapshot (existing state), send reset event
      if (snapshot) {
        latestActor.send({ type: 'reset' });
      }

      return latestActor;
    }

    // Validate version exists in registry
    if (!(version in ONBOARDING_MACHINE_REGISTRY)) {
      const availableVersions = Object.keys(ONBOARDING_MACHINE_REGISTRY).join(
        ', ',
      );
      throw new Error(
        `Unknown onboarding version: ${version}. Available versions: ${availableVersions}`,
      );
    }

    const versionedMachine = ONBOARDING_MACHINE_REGISTRY[version];
    if (!versionedMachine) {
      throw new Error(`Onboarding version ${version} not found`);
    }

    // Choose actors based on whether we need runtime behavior
    const actors = useRuntimeActors
      ? this.createRuntimeActors(cookie)
      : this.createNoOpActors();

    const machine = versionedMachine.provide({ actors });

    try {
      const actor = snapshot
        ? createActor(machine, { snapshot: machine.resolveState(snapshot) })
        : createActor(machine, {
            input: {
              version,
              existingData: {},
            } as AnyOnboardingInput,
          });

      return actor.start();
    } catch (e) {
      this.logger.error(
        e,
        {
          version,
          snapshot,
          cookie,
        },
        'ERROR_CREATING_ONBOARDING_ACTOR',
      );
      throw new HttpException(e.message, 500);
    }
  }
}
