import type { AuditService } from '@/modules/audit-log/audit-log.service';
import type { PrismaService } from '@/modules/prisma/prisma.service';
import type { SegmentService } from '@/modules/segment/segment.service';
import type { StripeService } from '@/modules/stripe/service/stripe.service';
import type Stripe from 'stripe';
import { getProductForAnalytics } from '@modules/shared/helpers/generic';
import Big from 'big.js';
import { differenceInYears } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';

// Will be injected by the service
let segmentService: SegmentService;
let prismaService: PrismaService;
let stripeService: StripeService;
let auditService: AuditService;

export function setSegmentService(service: SegmentService) {
  segmentService = service;
}

export function setPrismaService(service: PrismaService) {
  prismaService = service;
}

export function setStripeService(service: StripeService) {
  stripeService = service;
}

export function setAuditService(service: AuditService) {
  auditService = service;
}

// Map short source identifiers to full source strings
export const SOURCE_MAP: Record<string, string> = {
  questionnaire:
    'OnboardingEventEmitterService.processQuestionnaireStepIdentifyEvents',
  idPhoto: 'OnboardingEventEmitterService.handleIDPhotoEvents',
  facePhoto: 'OnboardingEventEmitterService.handlePhotoUploadEvents',
  email: 'OnboardingEventEmitterService.handleEmailEvents',
  paymentMethod: 'OnboardingEventEmitterService.handlePaymentMethodEvents',
};

// Helper to build userInfo object from context
export function getUserInfo(context: any) {
  return {
    email: context.email,
    firstName: context.firstName,
    lastName: context.lastName,
    phone: context.phone,
    gender: context.questionnaire?.gender,
    birthday: context.questionnaire?.birthDate,
    address: context.shippingInfo?.address1,
    state: context.shippingInfo?.state || context.state,
    city: context.shippingInfo?.city,
    zipcode: context.shippingInfo?.zip,
  };
}

// Helper to check if URL is null/invalid

export const handleTrack = async (payload: any) => {
  const { context, eventName, properties = {}, event } = payload.data;

  if (event.type === 'back') return;

  if (!context?.patientId) {
    console.warn('handleTrack: No patientId in context, skipping track');
    return;
  }

  if (!eventName) {
    console.warn('handleTrack: No eventName specified, skipping track');
    return;
  }

  const userInfo = getUserInfo(context);
  let trackProperties: Record<string, any> = { ...properties };

  // Build event-specific properties
  switch (eventName) {
    case 'OnboardingReject': {
      if (!context.rejected) break;
      const keys = Object.keys(context.questionnaire || {});
      const lastStep = keys[keys.length - 1];
      trackProperties = {
        reasonQuestion: lastStep,
        reasonAnswer: context.questionnaire?.[lastStep],
        step: eventName,
        ...userInfo,
      };
      break;
    }

    case 'QuestionnaireCompleted': {
      trackProperties = { ...userInfo };
      break;
    }

    case 'VisitStarted': {
      trackProperties = { ...userInfo };
      break;
    }

    case 'necessityStatementComplete': {
      // const objectives = context.questionnaire?.objectives;
      const objectives = event.value.objectives;
      if (objectives && objectives.length > 0) {
        trackProperties = {
          Type: 'Onboarding',
          Reason: objectives,
          ...userInfo,
        };
      } else {
        return;
      }
      break;
    }

    case 'AccountCreated': {
      const signupDate = new Date().getTime();

      trackProperties = {
        signupDate,
        email: userInfo.email,
        phone: userInfo.phone,
        state: context.state,
        firstName: userInfo.firstName,
        lastName: userInfo.lastName,
        name: `${userInfo.firstName} ${userInfo.lastName}`,
        SMSOptIn: context.getPromotionsSMS,
      };
      break;
    }

    case 'SignUp': {
      trackProperties = {};
      break;
    }

    case 'TreatmentChosen': {
      // Use product data from event since exit action runs before validateTreatment actor
      const products = event.output?.products || [];

      if (products.length === 0) {
        console.warn('TreatmentChosen: No products found in event');
        return;
      }

      const productNames: string[] = [];
      const productIDs: string[] = [];
      const productPrices: number[] = [];
      const ecommerceProducts: any[] = [];
      let coreProductName = '';

      products.forEach((product: any) => {
        productNames.push(product.label || product.name);
        productIDs.push(product.id);
        productPrices.push(product.price);
        ecommerceProducts.push({
          product_id: product.id,
          sku: product.id,
          name: product.label || product.name,
          price: product.price,
          image_url: product.image,
        });
        if (product.type === 'core') {
          coreProductName = product.name;
        }
      });

      const coreProduct = products.find((p: any) => p.type === 'core');
      const productAnalytics = coreProduct
        ? getProductForAnalytics({
            dosageLabel: coreProduct.dosageLabel,
            form: coreProduct.form,
            label: coreProduct.label,
          })
        : {};

      trackProperties = {
        productNames,
        productIDs,
        productPrices,
        coreProductName,
        products: ecommerceProducts,
        ...userInfo,
        ...productAnalytics,
        value: productPrices
          .reduce((acc, value) => acc.add(value), new Big(0))
          .toFixed(2),
      };
      break;
    }

    case 'IdentityVerificationStarted': {
      trackProperties = { ...userInfo };
      break;
    }

    case 'IDUploadSkipped': {
      trackProperties = { ...userInfo };
      break;
    }

    case 'IDUploaded': {
      const idPhoto = event.value?.['id-photo'] || context['id-photo'];

      if (!idPhoto) {
        // Photo was skipped
        await segmentService.track(context.patientId, 'IDUploadSkipped', {
          properties: { ...userInfo },
        });
        return;
      }

      // Photo was uploaded
      trackProperties = {
        IDUploaded: true,
        ...userInfo,
      };
      break;
    }

    case 'PhotoUploadSkipped': {
      trackProperties = { ...userInfo };
      break;
    }

    case 'PhotoUploaded': {
      const facePhoto = event.value?.['face-photo'] || context['face-photo'];

      if (!facePhoto) {
        // Photo was skipped
        await segmentService.track(context.patientId, 'PhotoUploadSkipped', {
          properties: { ...userInfo },
        });
        return;
      }

      // Photo was uploaded
      trackProperties = {
        photoUploaded: true,
        ...userInfo,
      };
      break;
    }

    case 'IdentityVerificationComplete': {
      trackProperties = { ...userInfo };
      break;
    }

    case 'VisitSummary': {
      trackProperties = { ...userInfo };
      break;
    }

    case 'CheckoutStarted': {
      // Use full product data from context (already enriched by validateTreatment actor)
      const products = context.products || [];

      const paymentProductNames: string[] = [];
      const paymentProductIDs: string[] = [];
      const paymentProductPrices: number[] = [];
      const paymentEcommerceProducts: any[] = [];

      products.forEach((product: any) => {
        paymentProductNames.push(product.label || product.name);
        paymentProductIDs.push(product.id);
        paymentProductPrices.push(product.price);
        paymentEcommerceProducts.push({
          image_url: product.image,
          name: product.label || product.name,
          price: product.price,
          product_id: product.id,
          sku: product.id,
        });
      });

      trackProperties = {
        ...userInfo,
        productIDs: paymentProductIDs,
        productNames: paymentProductNames,
        productPrices: paymentProductPrices,
        products: paymentEcommerceProducts,
      };
      break;
    }

    case 'CheckoutComplete': {
      // Use full product data from context (already enriched by validateTreatment actor)
      const products = context.products || [];

      const onboardedProductNames: string[] = [];
      const onboardedProductIDs: string[] = [];
      const onboardedProductPrices: number[] = [];
      let onboardedValue = 0;
      let onboardedCoreProductName = '';
      let onboardedCoreProductType = '';
      const onboardedEcommerceProducts: any[] = [];

      products.forEach((product: any) => {
        const productMetadata: any = product.metadata || {};
        onboardedProductNames.push(productMetadata?.label || product.name);
        onboardedProductIDs.push(product.id);
        onboardedProductPrices.push(product.price);
        onboardedValue += product.price;
        onboardedEcommerceProducts.push({
          sku: product.id,
          product_id: product.id,
          name: productMetadata?.label || product.name,
          image_url: product.image,
          price: product.price,
        });
        if (product.type === 'core') {
          onboardedCoreProductName = productMetadata?.label || product.name;
          onboardedCoreProductType = productMetadata?.form;
        }
      });

      const billingCycle =
        products?.find((p: any) => p.type === 'core')?.vials || 1;
      const coreProduct = products.find((p: any) => p.type === 'core');
      const productAnalytics = coreProduct
        ? getProductForAnalytics({
            dosageLabel: coreProduct.dosageLabel,
            form: coreProduct.form,
            label: coreProduct.label,
          })
        : {};

      trackProperties = {
        ...productAnalytics,
        currency: 'USD',
        productIDs: onboardedProductIDs,
        productNames: onboardedProductNames,
        productPrices: onboardedProductPrices,
        products: onboardedEcommerceProducts,
        value: onboardedValue,
        coreProductName: onboardedCoreProductName,
        coreProductType: onboardedCoreProductType,
        shippingAddress1: context.shippingInfo?.address1,
        shippingAddress2: context.shippingInfo?.address2,
        shippingCity: context.shippingInfo?.city,
        shippingState: context.shippingInfo?.state,
        shippingZipcode: context.shippingInfo?.zip,
        ...userInfo,
        billingCycle: `${billingCycle} Month`,
      };
      break;
    }

    case 'OnboardingComplete': {
      // Use full product data from context (already enriched by validateTreatment actor)
      const products = context.products || [];

      let onboardedValue = 0;
      products.forEach((product: any) => {
        onboardedValue += product.price;
      });

      trackProperties = {
        value: onboardedValue,
        currency: 'USD',
        ...userInfo,
        phone: '1' + userInfo.phone?.replaceAll(/\D/g, ''),
      };
      break;
    }

    case 'VerificationServiceSubmit': {
      trackProperties = { type: 'non-document', ...userInfo };
      break;
    }

    case 'VerificationServiceAccept': {
      trackProperties = { type: 'non-document', ...userInfo };
      break;
    }

    case 'VerificationServiceReject': {
      trackProperties = { type: 'non-document', ...userInfo };
      break;
    }

    default:
      console.warn(`handleTrack: Unknown event "${eventName}"`);
      return;
  }

  // Call Segment track
  await segmentService.track(
    context.patientId,
    eventName as any,
    {
      properties: trackProperties,
    },
    {
      includePatientTraits: true,
    },
  );
};

export const handleIdentify = async (payload: any) => {
  const { context, trait, source, event, startAfterSeconds } = payload.data;

  if (event.type === 'back') return;

  if (!context?.patientId) {
    console.warn('handleIdentify: No patientId in context, skipping identify');
    return;
  }

  if (!trait) {
    console.warn('handleIdentify: No trait specified, skipping identify');
    return;
  }

  const email = context.email || context.user?.email;
  if (!email) {
    console.warn('handleIdentify: No email found in context');
  }

  let traits: Record<string, any> = {};

  switch (trait) {
    case 'age': {
      const birthDate = event.value?.birthDate;
      if (birthDate) {
        try {
          const date = new Date(birthDate);
          if (isNaN(date.getTime())) {
            console.error('handleIdentify: Invalid birthDate', birthDate);
            break;
          }
          traits = {
            age: differenceInYears(new Date(), date),
            birthdayTimestamp: formatInTimeZone(date, 'UTC', 'yyyy-MM-dd'),
            birthdayString: formatInTimeZone(date, 'UTC', 'MM/dd/yyyy'),
          };
        } catch (error) {
          console.error('handleIdentify: Error processing birthDate', error);
        }
      }
      break;
    }

    case 'gender': {
      const gender = event.type;
      if (gender) {
        traits = {
          sex: gender,
        };
      }
      break;
    }

    case 'usingGLP1': {
      const usingGLP1 = event.type;
      if (usingGLP1 !== undefined) {
        traits = {
          currentlyOnGLP: usingGLP1 === 'yes',
        };
      }
      break;
    }

    case 'haveDiabetes': {
      const haveDiabetes = event.type;
      if (haveDiabetes !== undefined) {
        traits = {
          diabetesStatus: haveDiabetes === 'yes',
        };
      }
      break;
    }

    case 'weight': {
      const weight = event.value?.weight;
      if (weight !== undefined) {
        traits = {
          onboardingStartWeight: weight,
        };
      }
      break;
    }

    case 'desiredWeight': {
      const desiredWeight = event.value?.desiredWeight;
      if (desiredWeight !== undefined) {
        traits = {
          onboardingGoalWeight: desiredWeight,
        };
      }
      break;
    }

    case 'idPhoto': {
      // Only identify if photo was actually uploaded
      if (context['id-photo']) {
        traits = {
          IDUploaded: true,
        };
      }
      break;
    }

    case 'facePhoto': {
      // Only identify if photo was actually uploaded
      if (context['face-photo']) {
        traits = {
          photoUploaded: true,
        };
      }
      break;
    }

    case 'accountCreated': {
      const signupDate = new Date().getTime();

      traits = {
        email: email,
        phone: context.phone,
        state: context.state,
        firstName: context.firstName,
        lastName: context.lastName,
        name: `${context.firstName} ${context.lastName}`,
        SMSOptIn: context.getPromotionsSMS,
        signupDate,
        onboardingVersion: event.output?.onboardingVersion,
      };
      break;
    }

    case 'paymentMethod':
    case 'onboardingCompleted': {
      // Comprehensive identify for completed onboarding
      // Includes shipping, payment, and billing info
      const shippingInfo = context.shippingInfo;
      if (shippingInfo) {
        traits = {
          shippingAddress1: shippingInfo.address1,
          shippingAddress2: shippingInfo.address2,
          shippingCity: shippingInfo.city,
          shippingState: shippingInfo.state,
          shippingZipcode: shippingInfo.zip,
        };
      }

      // Fetch payment method data from database
      const patientPaymentMethod = await prismaService
        .primary()
        .patientPaymentMethod.findFirst({
          where: {
            patientId: context.patientId,
            default: true,
          },
        });

      if (!patientPaymentMethod) {
        console.warn(
          'handleIdentify: Could not find payment method for patient',
          context.patientId,
        );
        // Still set shipping and email even if payment method not found
        break;
      }

      const paymentMethod =
        patientPaymentMethod.data as unknown as Stripe.PaymentMethod;

      const stripeCustomer = await stripeService.getCustomer(
        paymentMethod.customer as string,
      );

      if (paymentMethod?.card) {
        const cardExpirationTime = getLastDayOfMonthTimestamp(
          paymentMethod.card.exp_year,
          paymentMethod.card.exp_month,
        );

        traits = {
          ...traits,
          cardExpiration: `${paymentMethod.card.exp_month}/${paymentMethod.card.exp_year}`,
          cardExpirationTime,
          cardLast4: paymentMethod.card.last4,
          cardExpMonth: paymentMethod.card.exp_month,
          cardExpYear: paymentMethod.card.exp_year,
          cardType: paymentMethod.card.brand,
        };
      }

      if (stripeCustomer?.address) {
        traits = {
          ...traits,
          billingAddress1: stripeCustomer.address.line1,
          billingAddress2: stripeCustomer.address.line2,
          billingCity: stripeCustomer.address.city,
          billingState: stripeCustomer.address.state,
          billingZipcode: stripeCustomer.address.postal_code,
        };
      }

      break;
    }

    default:
      console.warn(`handleIdentify: Unknown trait "${trait}"`);
      return;
  }

  if (email) {
    traits.email = email;
  }

  if (Object.keys(traits).length === 0) {
    console.warn(`handleIdentify: No traits extracted for "${trait}"`);
    return;
  }

  const fullSource = source ? SOURCE_MAP[source] || source : undefined;
  const identifyOptions: any = { source: fullSource };

  if (startAfterSeconds !== undefined) {
    identifyOptions.startAfterSeconds = startAfterSeconds;
  }

  await segmentService.identify(context.patientId, { traits }, identifyOptions);
};

export const handleAudit = async (payload: any) => {
  const { context, action, event, ...params } = payload.data;

  if (event.type === 'back') return;

  if (!context?.patientId) {
    console.warn('handleAudit: No patientId in context, skipping audit');
    return;
  }

  let details: Record<string, any> = { ...params };

  // Build event-specific details with business logic
  switch (action) {
    case 'ID_PHOTO_EVENT': {
      const photoType = params.type || 'id-photo';
      const photoData = context[photoType];
      const isSkipped = !photoData || photoData === null || photoData === '';

      if (isSkipped) {
        await auditService.append({
          patientId: context.patientId,
          action: 'ONBOARDING_UPLOAD_ID_PHOTO_SKIPPED',
          actorType: 'PATIENT',
          actorId: context.patientId,
          resourceType: 'PATIENT',
          resourceId: context.patientId,
          details: { type: photoType },
        });
      } else {
        await auditService.append({
          patientId: context.patientId,
          action: 'PATIENT_ID_PHOTO_UPDATED',
          actorType: 'PATIENT',
          actorId: context.patientId,
          resourceType: 'PATIENT',
          resourceId: context.patientId,
          details: { type: photoType },
        });
      }
      return;
    }

    case 'FACE_PHOTO_EVENT': {
      const photoType = params.type || 'face-photo';
      const photoData = context[photoType];
      const isSkipped = !photoData || photoData === null || photoData === '';

      if (isSkipped) {
        await auditService.append({
          patientId: context.patientId,
          action: 'ONBOARDING_UPLOAD_ID_PHOTO_SKIPPED',
          actorType: 'PATIENT',
          actorId: context.patientId,
          resourceType: 'PATIENT',
          resourceId: context.patientId,
          details: { type: photoType },
        });
      } else {
        await auditService.append({
          patientId: context.patientId,
          action: 'PATIENT_ID_PHOTO_UPDATED',
          actorType: 'PATIENT',
          actorId: context.patientId,
          resourceType: 'PATIENT',
          resourceId: context.patientId,
          details: { type: photoType },
        });
      }
      return;
    }

    case 'ONBOARDING_CHECKOUT_STARTED': {
      const products = context.products || [];
      const ecommerceProducts: any[] = [];

      products.forEach((product: any) => {
        const productMetadata: any = product.metadata || {};
        ecommerceProducts.push({
          sku: product.id,
          product_id: product.id,
          name: productMetadata?.label || product.name,
          image_url: product.image,
          price: product.price,
        });
      });

      details = { products: ecommerceProducts };
      break;
    }

    case 'ONBOARDING_CHECKOUT_COMPLETED': {
      const products = context.products || [];
      const ecommerceProducts: any[] = [];
      let value = 0;
      let coreProductName = '';
      let coreProductType = '';

      products.forEach((product: any) => {
        const productMetadata: any = product.metadata || {};
        ecommerceProducts.push({
          sku: product.id,
          product_id: product.id,
          name: productMetadata?.label || product.name,
          image_url: product.image,
          price: product.price,
        });
        value += product.price;

        if (product.type === 'core') {
          coreProductName = productMetadata?.label || product.name;
          coreProductType = productMetadata?.form;
        }
      });

      details = {
        products: ecommerceProducts,
        value,
        coreProductName,
        coreProductType,
        shippingAddress1: context.shippingInfo?.address1,
        shippingAddress2: context.shippingInfo?.address2,
        shippingCity: context.shippingInfo?.city,
        shippingState: context.shippingInfo?.state,
        shippingZip: context.shippingInfo?.zip,
      };
      break;
    }

    case 'PATIENT_VISIT_STARTED': {
      const userInfo = getUserInfo(context);
      details = {
        email: userInfo.email,
        firstName: userInfo.firstName,
        lastName: userInfo.lastName,
        phone: userInfo.phone,
      };
      break;
    }

    case 'PATIENT_ACCOUNT_CREATED': {
      details = {
        date: new Date().toISOString(),
        state: context.state,
      };
      break;
    }

    // For most actions, details are passed directly as params or are empty
    default:
      // details already contains params spread above
      break;
  }

  await auditService.append({
    patientId: context.patientId,
    action,
    actorType: 'PATIENT',
    actorId: context.patientId,
    resourceType: 'PATIENT',
    resourceId: context.patientId,
    details,
  });
};

// Helper to get last day of month timestamp
function getLastDayOfMonthTimestamp(year: number, month: number): number {
  const nextMonth = new Date(year, month, 1);
  nextMonth.setDate(nextMonth.getDate() - 1);
  return nextMonth.getTime();
}
