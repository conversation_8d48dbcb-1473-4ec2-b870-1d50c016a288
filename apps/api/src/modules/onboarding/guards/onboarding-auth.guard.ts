import type { CanActivate, ExecutionContext } from '@nestjs/common';
import type { Request } from 'express';
import { Injectable, UnauthorizedException } from '@nestjs/common';

import type { OnboardingCookie } from '../types/cookie.types';
import { CognitoService } from '../../auth/cognito.service';
import { JwtService } from '../../auth/jwt.service';

@Injectable()
export class OnboardingAuthGuard implements CanActivate {
  private readonly cookieName = 'willow-onboarding';

  constructor(
    private readonly jwtService: JwtService,
    private readonly cognitoService: CognitoService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const cookieValue = request.cookies?.[this.cookieName];

    // No cookie means pre-signup state, allow access
    if (!cookieValue) return true;

    let parsedCookie: OnboardingCookie;
    try {
      parsedCookie = JSON.parse(cookieValue) as OnboardingCookie;
    } catch (error) {
      // If parsing fails, allow access (cookie might be corrupted or not set)
      return true;
    }

    // Check if we're in pre-signup state
    const currentState = parsedCookie.currentState;
    const isPreSignup = currentState?.startsWith('preSignup') ?? true;

    // Pre-signup state, no authentication required
    if (isPreSignup) return true;

    // Post-signup state, authentication required
    const accessToken = parsedCookie.authTokens?.accessToken;
    if (!accessToken) {
      throw new UnauthorizedException('Missing auth token');
    }

    try {
      // Verify the JWT token
      const decodedToken = await this.jwtService.verifyToken(accessToken);

      // Set user context similar to how CognitoJwtStrategy does it
      const role = decodedToken['cognito:groups']?.[0];
      const userId = decodedToken.sub;

      if (!role || !userId || role !== 'Patient') {
        throw new UnauthorizedException('Invalid token payload');
      }

      // Set the user context on the request
      request.user = { userId, role };

      return true;
    } catch (tokenError) {
      // Access token verification failed, try to refresh
      const refreshToken = parsedCookie.authTokens?.refreshToken;
      if (!refreshToken) {
        throw new UnauthorizedException('Missing refresh token');
      }

      try {
        // Get the role from the existing token payload (even if expired)
        const role = parsedCookie.authTokens?.role || 'Patient';

        // Refresh tokens
        const session = await this.cognitoService.refresh(refreshToken, role);
        const newAccessToken = session.getAccessToken().getJwtToken();
        const newRefreshToken = session.getRefreshToken().getToken();

        // Store refreshed tokens in request for interceptor to process
        request['refreshedTokens'] = {
          ...parsedCookie.authTokens,
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
        };

        // Verify the new access token and set user context
        const decodedToken = await this.jwtService.verifyToken(newAccessToken);
        const tokenRole = decodedToken['cognito:groups']?.[0];
        const userId = decodedToken.sub;

        if (!tokenRole || !userId) {
          throw new UnauthorizedException(
            'Invalid token payload after refresh',
          );
        }

        // Set the user context on the request
        request.user = {
          userId,
          role: tokenRole,
        };

        return true;
      } catch (refreshError) {
        throw new UnauthorizedException('Token refresh failed');
      }
    }
  }
}
