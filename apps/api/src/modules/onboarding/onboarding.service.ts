import { PatientPersistence } from '@/adapters/persistence/database/patient.persistence';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PatientSignInUseCase } from '@/modules/patient/use-cases/patient-sign-in-use.case';
import { StripeService } from '@/modules/stripe/service/stripe.service';
import {
  handleAudit,
  handleIdentify,
  handleTrack,
  setAuditService,
  setPrismaService,
  setSegmentService,
  setStripeService,
} from '@modules/onboarding/helpers/tracking';
import { PrismaService } from '@modules/prisma/prisma.service';
import { SegmentService } from '@modules/segment/segment.service';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { waitFor } from 'xstate';

import type { OnboardingVersion } from './states';
import type {
  AnyOnboardingSnapshot,
  OnboardingCookie,
} from './types/cookie.types';
import { OnboardingActorFactory } from './helpers/onboarding-actor-factory';
import { OnboardingPersistence } from './helpers/onboarding-persistence';
import { OnboardingVersionManager } from './helpers/onboarding-version-manager';
import { OnboardingCookieService } from './services/onboarding-cookie.service';
import { OnboardingValidateDiscountUseCase } from './use-cases/onboarding-validate-discount.use-case';

// Generic type to get context type from version
export type OnboardingContextForVersion<T extends OnboardingVersion> = any; // Simplified for now

// Content configuration that can be defined in state machine metadata
export interface OnboardingContentConfig {
  title?: string;
  subtitle?: string | string[];
  description?: string;
  buttonText?: string;
}

// Generic return type for getCurrentOnboardingState
export interface OnboardingStateResult<T extends OnboardingVersion> {
  state: any;
  context: OnboardingContextForVersion<T>;
  events: string[];
  stepName: string;
  percentage: number;
  content?: OnboardingContentConfig;
  component: string | undefined;
}

@Injectable()
export class OnboardingService {
  private readonly SECTION_NAMES = {
    preSignup: 'Account Creation',
    questionnaire: 'Health Questionnaire',
    selectTreatmentType: 'Select Your Medication',
    selectTreatment: 'Select Your Medication',
    identityVerification: 'Identity Verification',
    ssnCheck: 'Identity Verification',
    ssnSuccess: 'Identity Verification',
    info: 'Virtual Doctor Visit',
    uploadIdPhoto: 'Upload Your Photos',
    uploadFacePhoto: 'Upload Your Photos',
    shippingInfo: 'Checkout',
    paymentInfo: 'Checkout',
    reviewOrder: 'Checkout',
    confirmPayment: 'Checkout',
    processingPayment: 'Checkout',
    onboarded: 'Completed',
  };

  constructor(
    private readonly cookieService: OnboardingCookieService,
    private readonly actorFactory: OnboardingActorFactory,
    private readonly persistence: OnboardingPersistence,
    private readonly patientPersistence: PatientPersistence,
    private readonly prismaService: PrismaService,
    @Inject(forwardRef(() => PatientSignInUseCase))
    private readonly patientSignInUseCase: PatientSignInUseCase,
    private readonly segmentService: SegmentService,
    private readonly stripeService: StripeService,
    private readonly auditService: AuditService,
    private readonly onboardingValidateDiscountUseCase: OnboardingValidateDiscountUseCase,
    private readonly versionManager: OnboardingVersionManager,
  ) {
    // Initialize tracking handlers with services
    setSegmentService(segmentService);
    setPrismaService(prismaService);
    setStripeService(stripeService);
    setAuditService(auditService);
  }

  /**
   * Fetch onboarding snapshot from database using patientId
   */
  async fetchSnapshotFromDatabase(
    patientId: string,
  ): Promise<AnyOnboardingSnapshot | null> {
    try {
      const patient =
        await this.patientPersistence.getOnboardingProfile(patientId);
      return patient.onboardingState as AnyOnboardingSnapshot;
    } catch (error) {
      console.error(
        'Failed to fetch onboarding snapshot from database:',
        error,
      );
      return null;
    }
  }

  /**
   * Get or create snapshot from cookie, falling back to database if needed
   */
  public async getSnapshotFromCookieOrDatabase(
    cookie: OnboardingCookie,
  ): Promise<AnyOnboardingSnapshot> {
    // If cookie has authTokens, ALWAYS fetch from database (post-signup phase)
    // This ensures we get the latest state and ignore any outdated stateSnapshot in cookie
    if (cookie?.authTokens?.patientId) {
      const snapshot = await this.fetchSnapshotFromDatabase(
        cookie.authTokens.patientId,
      );
      if (!snapshot) {
        throw new BadRequestException(
          'Missing onboarding state. Call GET /onboarding/state first.',
        );
      }
      return snapshot;
    }

    // Only use cookie stateSnapshot if no authTokens (pre-signup phase)
    if ((cookie as any)?.stateSnapshot) {
      return (cookie as any).stateSnapshot as AnyOnboardingSnapshot;
    }

    throw new BadRequestException(
      'Missing onboarding state. Call GET /onboarding/state first.',
    );
  }

  /**
   * Attach discount to snapshot after validation
   */
  private async attachDiscountToSnapshot(
    snapshot: AnyOnboardingSnapshot,
    discount: { type: 'referral' | 'coupon'; code: string },
    cookie?: OnboardingCookie,
  ): Promise<AnyOnboardingSnapshot> {
    try {
      // Validate the discount code first and get full info
      const validatedDiscount =
        await this.onboardingValidateDiscountUseCase.execute({ discount });

      // Don't allow referral attachment if patient already exists
      // Referrals must be attached during signup, not after
      if (discount.type === 'referral') {
        const hasPatientId = snapshot.context?.patientId;

        if (hasPatientId) {
          // Silently ignore referral code - patient already created
          return snapshot;
        }
      }

      // Check if patient is still in onboarding (for promo codes after signup)
      if (cookie?.authTokens?.patientId) {
        const patient = await this.patientPersistence.getOnboardingProfile(
          cookie.authTokens.patientId,
        );

        // Only allow discount attachment for onboarding patients
        if (patient.status !== 'onboardingPending') {
          // Silently ignore discount if patient already completed onboarding
          return snapshot;
        }
      }

      // Apply discount attachment logic
      const currentDiscount = snapshot.context?.discount;

      // Don't replace existing referral codes
      if (
        discount.type === 'referral' &&
        currentDiscount?.type === 'referral'
      ) {
        return snapshot;
      }

      // Build full discount object with validated info
      const fullDiscount = {
        type: discount.type,
        code: validatedDiscount.code,
        label: validatedDiscount.label,
        value: validatedDiscount.value,
        valueType: validatedDiscount.valueType,
      };

      // Update snapshot with new discount
      return {
        ...snapshot,
        context: {
          ...snapshot.context,
          discount: fullDiscount,
        },
      };
    } catch (error) {
      // If validation fails, add transient validation error to context
      const errorMessage =
        discount.type === 'referral'
          ? `Referral code ${discount.code} is invalid`
          : `Promo code ${discount.code} is invalid`;

      return {
        ...snapshot,
        context: {
          ...snapshot.context,
          validationError: {
            type: 'ValidationError',
            message: errorMessage,
            errors: [
              {
                field: 'discount',
                message: errorMessage,
              },
            ],
          },
        },
      };
    }
  }

  /**
   * Initialize or restore onboarding from cookie
   */
  async getOrInitCurrentOnboarding(promo?: string, refcode?: string) {
    const existing = this.cookieService.tryGetCookie();

    // Determine discount from query params (referral takes precedence)
    let discount: { type: 'referral' | 'coupon'; code: string } | undefined;
    if (refcode) {
      discount = { type: 'referral', code: refcode };
    } else if (promo) {
      discount = { type: 'coupon', code: promo };
    }

    if (existing?.version) {
      try {
        const snapshot = await this.getSnapshotFromCookieOrDatabase(existing);
        const version = existing.version as OnboardingVersion;

        // If discount is provided, attach it to the snapshot
        let updatedSnapshot = snapshot;
        let discountWasAttached = false;
        if (discount) {
          updatedSnapshot = await this.attachDiscountToSnapshot(
            snapshot,
            discount,
            existing,
          );
          // Check if discount was actually attached (wasn't already present)
          discountWasAttached = updatedSnapshot !== snapshot;
        }

        const actor = await this.actorFactory.createOnboardingActor(
          version,
          updatedSnapshot,
          false, // Use no-op actors for state inspection
        );

        // If discount was attached, update the cookie with the new snapshot
        if (discountWasAttached) {
          const updatedCookie = {
            ...existing,
            stateSnapshot: (
              actor as any
            ).getPersistedSnapshot() as AnyOnboardingSnapshot,
          } as OnboardingCookie;
          this.cookieService.setCookie(updatedCookie);

          // If authenticated, also persist to database
          if (existing.authTokens?.patientId) {
            await this.persistence.persistSnapshotOnly(
              existing.authTokens.patientId,
              (actor as any).getPersistedSnapshot() as AnyOnboardingSnapshot,
              version,
            );
          }
        }

        return await this.getCurrentOnboardingState(
          version,
          actor.getSnapshot(),
        );
      } catch {
        // Fall through to initialize new onboarding
      }
    }

    // Get default version from admin settings (respects database override)
    const version = await this.versionManager.getDefaultOnboardingVersion();

    let actor = await this.actorFactory.createOnboardingActor(
      version,
      undefined,
      false, // Use no-op actors for initial creation
    );

    let snapshot = actor.getSnapshot();

    // If discount is provided, attach it to the new onboarding
    if (discount) {
      const updatedSnapshot = await this.attachDiscountToSnapshot(
        (actor as any).getPersistedSnapshot() as AnyOnboardingSnapshot,
        discount,
        undefined,
      );

      // Recreate actor with updated snapshot
      actor = await this.actorFactory.createOnboardingActor(
        version,
        updatedSnapshot,
        false,
      );
      snapshot = actor.getSnapshot();
    }

    // Create the cookie object with full stateSnapshot (use the updated actor)
    const newCookie = {
      version,
      startedAt: new Date().toISOString(),
      currentState: this.getStateString(snapshot.value),
      stateSnapshot: (
        actor as any
      ).getPersistedSnapshot() as AnyOnboardingSnapshot,
    } as OnboardingCookie;

    // Persist cookie
    this.cookieService.setCookie(newCookie);

    return await this.getCurrentOnboardingState(version, snapshot);
  }

  /**
   * Set up event listeners on an actor to collect emitted events
   */
  setupEventListeners(actor: any): Array<{ type: string; data: any }> {
    const emittedEvents: Array<{ type: string; data: any }> = [];

    actor.on('track', (event) =>
      emittedEvents.push({ type: 'track', data: event }),
    );
    actor.on('identify', (event) =>
      emittedEvents.push({ type: 'identify', data: event }),
    );
    actor.on('audit', (event) =>
      emittedEvents.push({ type: 'audit', data: event }),
    );

    return emittedEvents;
  }

  /**
   * Process collected events through handlers
   */
  async processEvents(events: Array<{ type: string; data: any }>) {
    for (const event of events) {
      switch (event.type) {
        case 'track':
          await handleTrack(event.data);
          break;
        case 'identify':
          await handleIdentify(event.data);
          break;
        case 'audit':
          await handleAudit(event.data);
          break;
      }
    }
  }

  /**
   * Handle onboarding event dispatch
   */
  async dispatch(validatedEvent: any, cookie: OnboardingCookie) {
    if (!cookie) {
      throw new UnauthorizedException('Missing onboarding cookie');
    }

    const version = cookie.version as OnboardingVersion;
    const snapshot = await this.getSnapshotFromCookieOrDatabase(cookie);

    // Create actor with runtime actors for actual transitions
    const actor = await this.actorFactory.createOnboardingActor(
      version,
      snapshot,
      true,
      cookie,
    );

    // Set up event listeners to collect emitted events
    const emittedEvents = this.setupEventListeners(actor);

    // Check if transition is valid
    const can = actor.getSnapshot().can(validatedEvent);
    if (!can) {
      throw new BadRequestException('Invalid transition');
    }

    // Send event
    actor.send(validatedEvent);

    // Wait for async operations to complete
    const newSnapshot = await this.waitForStateResolution(actor);

    // Process all collected events sequentially with async operations
    await this.processEvents(emittedEvents);

    if (newSnapshot.status === 'error') {
      throw new BadRequestException('Machine error after transition');
    }

    // Check if auto-login is required (existing user with valid credentials)
    const authData = newSnapshot.context?.authData;
    if (authData?.requiresAutoLogin) {
      try {
        // Call PatientSignInUseCase to handle full sign-in flow
        // This will load the existing user's actual onboarding state from DB
        const signInResponse = await this.patientSignInUseCase.execute({
          email: authData.email,
          password: authData.password,
        });

        // Return the sign-in response directly
        // The sign-in use case has already set the cookie with auth tokens and proper state
        // Don't return a cookie here - it would overwrite the cookie that PatientSignInUseCase just set

        // Clear onboarding cookie if patient has completed onboarding (has dashboard)
        // Similar to the /onboarded endpoint behavior
        if (signInResponse.dashboard) {
          this.cookieService.clearCookie();
        }

        return {
          response: signInResponse,
          cookie: undefined,
        };
      } catch (error) {
        // Auto-login failed (wrong password or other auth error)
        // Get the current onboarding state and add validation error
        const response = await this.getCurrentOnboardingState(
          version,
          newSnapshot,
        );

        return {
          response: {
            ...response,
            validationError: {
              type: 'ValidationError',
              message: 'Email already exists',
              errors: [
                {
                  field: 'email',
                  message: 'Email already exists',
                },
              ],
            },
          },
          cookie, // Keep original cookie unchanged
        };
      }
    }

    // Persist changes
    const updatedCookie = await this.persistence.persistAll(
      cookie,
      actor,
      version,
    );

    const response = await this.getCurrentOnboardingState(version, newSnapshot);

    return { response, cookie: updatedCookie };
  }

  /**
   * Get current onboarding state with validation error handling
   */
  async getCurrentOnboardingState<T extends OnboardingVersion>(
    version: T,
    snapshot: any,
  ): Promise<OnboardingStateResult<T>> {
    // Build an actor with no-op runtime actors to avoid invoking real services
    const actor = await this.actorFactory.createOnboardingActor(
      version,
      snapshot,
      false, // No-op actors
    );

    const onboardingSnapshot = actor.getSnapshot();
    const { stepName, percentage, content, component } =
      this.getStateMetadata(onboardingSnapshot);

    // Get available events
    const events = this.getAvailableEvents(onboardingSnapshot, actor);

    const stateResult: OnboardingStateResult<T> = {
      state: onboardingSnapshot.value,
      context: onboardingSnapshot.context,
      events,
      stepName,
      percentage,
      content,
      component,
    };

    // Handle validation error clearing and response preparation
    return await this.prepareClientResponse(
      stateResult,
      onboardingSnapshot,
      version,
    );
  }

  /**
   * Prepare client response with validation error handling
   */
  public async prepareClientResponse(
    stateResult: any,
    snapshot: any,
    version?: OnboardingVersion,
  ) {
    // Extract validation error from context if present (ephemeral)
    const validationError = snapshot.context?.validationError;

    // Extract context from stateResult and clean sensitive data
    const { context, ...responseWithoutContext } = stateResult;

    // Remove authData and validationError from context before sending to client
    const { authData, validationError: _, ...cleanContext } = context || {};

    // Get version from parameter or fall back to cookie
    const responseVersion =
      version ?? this.cookieService.tryGetCookie()?.version;

    return {
      patientId: context.patientId,
      ...responseWithoutContext,
      context: cleanContext, // Add cleaned context to the response
      ...(validationError && { validationError }),
      ...(responseVersion && { version: responseVersion }),
    };
  }

  /**
   * Wait for async state resolution
   */
  private async waitForStateResolution(actor: any): Promise<any> {
    try {
      return await waitFor(
        actor,
        (state) => {
          const currentStateValue = this.getStateString(state.value);
          return (
            !currentStateValue.includes('checkingState') &&
            !currentStateValue.includes('creatingAccount') &&
            !currentStateValue.includes('addingToWaitingList') &&
            !currentStateValue.includes('validatingName') &&
            !currentStateValue.includes('verifySsn') &&
            !currentStateValue.includes('validatingTreatmentType') &&
            !currentStateValue.includes('validatingTreatment') &&
            !currentStateValue.includes('validateAndSaveShipping')
          );
        },
        { timeout: 30000 },
      );
    } catch (error) {
      // If timeout or error, return current snapshot
      console.error('Error waiting for state resolution:', error);
      return actor.getSnapshot();
    }
  }

  /**
   * Get available events from current state
   */
  private getAvailableEvents(snapshot: any, actor: any): string[] {
    const stateValue = snapshot.value;
    const statePath = this.getStatePath(stateValue);

    let events: string[] = [];
    try {
      // Access the machine config through the actor
      const machineConfig = (actor as any).logic?.config;

      if (machineConfig?.states) {
        // Navigate to the current state node
        let currentStateConfig = machineConfig.states;

        for (const key of statePath) {
          if (currentStateConfig[key]) {
            // Get events from the current level
            const stateNode = currentStateConfig[key];
            if (stateNode?.on) {
              const stateEvents = Object.keys(stateNode.on).filter(
                (event) => event !== '',
              );
              events.push(...stateEvents);
            }

            // Navigate deeper if there are nested states
            currentStateConfig = currentStateConfig[key].states || {};
          }
        }
      }
    } catch (error) {
      console.warn('Could not extract events from machine config:', error);
      events = [];
    }

    return [...new Set(events)];
  }

  private getStatePath(stateValue: any): string[] {
    if (typeof stateValue === 'string') {
      return [stateValue];
    }
    // Handle nested state objects like { questionnaire: 'age' }
    const keys = Object.keys(stateValue);
    if (keys.length === 1) {
      return [keys[0], ...this.getStatePath(stateValue[keys[0]])];
    }
    return [];
  }

  private getStateMetadata(snapshot: any): {
    stepName: string;
    percentage: number;
    content?: OnboardingContentConfig;
    component: string | undefined;
  } {
    const stateValue = snapshot.value;

    // Handle final state
    if (stateValue === 'onboarded') {
      return {
        stepName: this.SECTION_NAMES['onboarded'],
        percentage: 100,
        component: undefined,
      };
    }

    // Get state path (e.g., ['questionnaire', 'age'] or ['preSignup', 'stateSelection'])
    const statePath = this.getStatePath(stateValue);
    const rootState = statePath[0];

    // Navigate through the state configuration to find the current state's metadata
    let absoluteStep = 0;
    let name = '';
    let content: OnboardingContentConfig | undefined;
    let component: string | undefined = undefined;
    let stateConfig = snapshot.machine.config.states;

    for (let i = 0; i < statePath.length; i++) {
      const stateName = statePath[i];
      if (stateConfig && stateConfig[stateName]) {
        // Get step, name, and content from the current state's meta if they exist
        if (stateConfig[stateName]?.meta) {
          absoluteStep = stateConfig[stateName].meta.step || absoluteStep;
          name = stateConfig[stateName].meta.name || name;
          component = stateConfig[stateName].meta.component || component;

          // Extract content from meta if it exists
          if (stateConfig[stateName].meta.content) {
            content = {
              title: stateConfig[stateName].meta.content.title,
              subtitle: stateConfig[stateName].meta.content.subtitle,
              description: stateConfig[stateName].meta.content.description,
              buttonText: stateConfig[stateName].meta.content.buttonText,
            };

            // Remove undefined properties
            Object.keys(content).forEach((key) => {
              if (
                content![key as keyof OnboardingContentConfig] === undefined
              ) {
                delete content![key as keyof OnboardingContentConfig];
              }
            });

            // If no content properties remain, set to undefined
            if (Object.keys(content).length === 0) {
              content = undefined;
            }
          }
        }

        // Navigate to nested states for the next iteration
        if (i < statePath.length - 1 && stateConfig[stateName].states) {
          stateConfig = stateConfig[stateName].states;
        }
      }
    }

    // If no name was found in meta, fall back to SECTION_NAMES
    if (!name) {
      name = this.SECTION_NAMES[rootState] || '';
    }

    // Calculate timeline-based percentage
    let percentage: number;

    if (rootState === 'preSignup') {
      // Timeline 1: Pre-signup (4 steps)
      const currentStep = Math.min(absoluteStep, 4);
      percentage = Math.round((currentStep / 4) * 100);
    } else {
      // Timeline 2: Main onboarding (questionnaire + post-questionnaire = 24 steps)
      let currentStep: number;

      if (rootState === 'questionnaire') {
        // Questionnaire steps: absolute steps 5-19 map to timeline steps 1-15
        currentStep = absoluteStep - 4; // Step 5 becomes step 1, step 19 becomes step 15
      } else {
        // Post-questionnaire states
        const postQuestionnaireStepMap: Record<string, number> = {
          selectTreatmentType: 16,
          selectTreatment: 17,
          info: 18,
          uploadIDPhoto: 19,
          uploadFacePhoto: 20,
          visitCompletion: 21,
          summary: 22,
          shipping: 23,
          payment: 24,
        };

        currentStep = postQuestionnaireStepMap[rootState] || 16;
      }

      percentage = Math.round((currentStep / 24) * 100);
    }

    return {
      stepName: name,
      percentage,
      content,
      component,
    };
  }

  private getStateString(value: any): string {
    if (typeof value === 'string') return value;
    if (typeof value === 'object' && value !== null) {
      const keys = Object.keys(value as Record<string, any>);
      if (keys.length === 1) {
        const key = keys[0];
        const nestedValue = (value as any)[key];
        if (typeof nestedValue === 'object') {
          return `${key}.${this.getStateString(nestedValue)}`;
        }
        return `${key}.${nestedValue}`;
      }
    }
    return String(value);
  }

  /**
   * Create actor from cookie for specific operations (like payment setup)
   */
  async createActorFromCookie(cookie: OnboardingCookie) {
    if (!cookie?.version) {
      throw new BadRequestException(
        'Missing onboarding cookie. Call GET /onboarding/state first.',
      );
    }

    const version = cookie.version as OnboardingVersion;
    const snapshot = await this.getSnapshotFromCookieOrDatabase(cookie);

    const actor = await this.actorFactory.createOnboardingActor(
      version,
      snapshot,
      true, // Use runtime actors
      cookie,
    );

    const actorSnapshot = actor.getSnapshot();
    const statePath = this.getStateString(actorSnapshot.value);
    const isPreSignup = statePath.startsWith('preSignup');

    return {
      version,
      actor,
      snapshot: actorSnapshot,
      cookie,
      statePath,
      isPreSignup,
    };
  }

  /**
   * Ensures an actor exists from cookie or initializes current onboarding
   * Equivalent to OnboardingDispatchService.ensureActorFromCookieOrInitCurrent()
   */
  async ensureActorFromCookieOrInitCurrent() {
    const existing = this.cookieService.tryGetCookie();

    if (existing?.version) {
      try {
        const stateSnapshot =
          await this.getSnapshotFromCookieOrDatabase(existing);
        const version = existing.version as OnboardingVersion;
        const actor = await this.actorFactory.createOnboardingActor(
          version,
          stateSnapshot,
          true, // Use runtime actors
          existing,
        );

        const snapshot = actor.getSnapshot();
        const response = await this.getCurrentOnboardingState(
          version,
          snapshot,
        );

        return {
          version,
          snapshot,
          response,
          cookie: existing,
        };
      } catch {
        // Fall through to initialize new onboarding
      }
    }

    // Get default version from admin settings (respects database override)
    const version = await this.versionManager.getDefaultOnboardingVersion();

    const actor = await this.actorFactory.createOnboardingActor(
      version,
      undefined,
      true, // Use runtime actors
    );

    const snapshot = actor.getSnapshot();
    const response = await this.getCurrentOnboardingState(version, snapshot);

    // Create the cookie object with full stateSnapshot
    const newCookie = {
      version,
      startedAt: new Date().toISOString(),
      currentState: this.getStateString(snapshot.value),
      stateSnapshot: (
        actor as any
      ).getPersistedSnapshot() as AnyOnboardingSnapshot,
    } as OnboardingCookie;

    // Persist cookie
    this.cookieService.setCookie(newCookie);

    return { version, snapshot, response, cookie: newCookie };
  }

  /**
   * Creates an actor from a given snapshot
   * Equivalent to OnboardingDispatchService.createActorFromSnapshot()
   */
  async createActorFromSnapshot(
    version: OnboardingVersion,
    snapshot: AnyOnboardingSnapshot,
  ) {
    const actor = await this.actorFactory.createOnboardingActor(
      version,
      snapshot,
      true, // Use runtime actors
    );

    const actorSnapshot = actor.getSnapshot();
    const statePath = this.getStateString(actorSnapshot.value);
    const isPreSignup = statePath.startsWith('preSignup');

    return {
      version,
      actor,
      snapshot: actorSnapshot,
      statePath,
      isPreSignup,
    };
  }
}
