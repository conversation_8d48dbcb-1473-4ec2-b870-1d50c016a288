export interface ValidationErrorField {
  field: string;
  message: string;
}

export class OnboardingValidationError extends Error {
  constructor(
    message: string,
    public readonly errors?: ValidationErrorField[],
  ) {
    super(message);
    this.name = 'OnboardingValidationError';
  }

  toContext() {
    return {
      validationError: {
        type: 'ValidationError' as const,
        message: this.message,
        errors: this.errors,
      },
    };
  }
}
