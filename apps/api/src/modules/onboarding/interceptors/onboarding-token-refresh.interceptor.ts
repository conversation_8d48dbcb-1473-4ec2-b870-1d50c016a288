import type {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  NestInterceptor,
} from '@nestjs/common';
import type { Observable } from 'rxjs';
import { Injectable } from '@nestjs/common';
import { tap } from 'rxjs/operators';

import type { OnboardingAuthTokens } from '../types/cookie.types';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';

@Injectable()
export class OnboardingTokenRefreshInterceptor implements NestInterceptor {
  constructor(private readonly cookieService: OnboardingCookieService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    return next.handle().pipe(
      tap(() => {
        const refreshedTokens = request['refreshedTokens'] as
          | OnboardingAuthTokens
          | undefined;

        if (refreshedTokens) {
          // Update cookie with the refreshed tokens
          this.cookieService.updateCookie({
            authTokens: refreshedTokens,
          });

          // Clean up the request object
          delete request['refreshedTokens'];
        }
      }),
    );
  }
}
