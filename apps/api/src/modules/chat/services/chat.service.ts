import { ExecutionContext, runInDbTransaction } from '@/helpers/transaction';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { OrchestrationService } from '@/modules/shared/orchestration/orchestration.service';
import { roles } from '@modules/auth/types/roles';
import { SendMessageInput } from '@modules/chat/types/chat.types';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  Conversation,
  ConversationStatus,
  ConversationType,
  Patient,
  Prisma,
  User,
} from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

import { AuditLog } from '@willow/utils/audit-log';

import { ChatEvent } from '../events/chat.events';
import { ChatImageService } from './chat.image.service';
import { PatientMessageRouterService } from './patient-message-router.service';

// Error types
export class ConversationNotFoundError extends Error {
  constructor(conversationId: string) {
    super(`Conversation ${conversationId} not found.`);
    this.name = 'ConversationNotFoundError';
  }
}

export class UserNotConversationMemberError extends Error {
  constructor() {
    super('User is not a member of this conversation');
    this.name = 'UserNotConversationMemberError';
  }
}

export class AdminNotFoundError extends Error {
  constructor() {
    super('Admin user not found');
    this.name = 'AdminNotFoundError';
  }
}

type EnsureConversationTypeParams =
  | {
      conversationType: 'patientDoctor';
      doctorUserId: string;
      patientUserId: string;
      prisma?: PrismaTransactionalClient;
    }
  | {
      conversationType: 'doctorAdmin';
      doctorUserId: string;
      patientUserId: string;
      adminUserId?: string;
      prisma?: PrismaTransactionalClient;
    };

type GetConversationWatchersReturnType = Awaited<
  ReturnType<ChatService['getConversationWatchers']>
>;
type CreateMessageReturnType = Awaited<
  ReturnType<ChatService['createMessage']>
>;

type ChatParticipant = {
  id: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    type: string;
  };
  imageUrl: string;
};

type ConversationWithPatient = Conversation & {
  patient?: Patient & { user: User };
};

type ConversationMember = {
  user: {
    id: string;
    firstName: string;
    lastName: string;
    type: string;
    doctor?: { id: string };
    patient?: { id: string };
    admin?: { id: string };
  };
};

export type AdminConversationFilter =
  | 'myInbox'
  | 'mySent'
  | 'myUnreplied'
  | 'allSent'
  | 'allUnreplied'
  | 'all'
  | 'unassigned'
  | 'closed';

// Configuration constants
const CLOSED_CONVERSATION_DAYS = 7;
const DEFAULT_PAGE_SIZE = 20;

@Injectable()
export class ChatService {
  private readonly logger: LoggerService;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly chatImageService: ChatImageService,
    private readonly auditService: AuditService,
    private readonly patientMessageRouterService: PatientMessageRouterService,
    private readonly treatmentService: TreatmentService,
    private readonly orchestrationService: OrchestrationService,
    private readonly loggerFactory: LoggerFactory,
    private readonly configService: ConfigService,
    private readonly segment: SegmentService,
  ) {
    this.logger = this.loggerFactory.createLogger(ChatService.name);
  }

  /**
   * Helper method to generate image URLs for different user types
   */
  private getImageUrl(userType: string, imagePath: string): string {
    const baseUrl = this.configService.get('NEXT_PUBLIC_API_S3_URL');
    const patientPhotosUrl = this.configService.get(
      'NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL',
    );
    const timestamp = new Date().getTime();

    if (userType === 'doctor') {
      return `${baseUrl}/${imagePath}`;
    } else if (userType === 'patient') {
      return `${patientPhotosUrl}/${imagePath}?${timestamp}`;
    }
    return '';
  }

  /**
   * Helper method to build participant info for conversations
   */
  private buildParticipants(
    doctors: Array<{ id: string; image?: string; user: any }>,
    patients: Array<{ id: string; facePhoto?: string; user: any }>,
    admins?: Array<{ id: string; user: any }>,
  ): Record<string, ChatParticipant> {
    const participants: Record<string, ChatParticipant> = {};

    // Add doctors
    doctors.forEach((doctor) => {
      participants[doctor.user.id] = {
        id: doctor.id,
        user: doctor.user,
        imageUrl: this.getImageUrl('doctor', doctor.image || ''),
      };
    });

    // Add patients
    patients.forEach((patient) => {
      participants[patient.user.id] = {
        id: patient.id,
        user: patient.user,
        imageUrl: this.getImageUrl('patient', patient.facePhoto || ''),
      };
    });

    // Add admins (if provided)
    if (admins) {
      admins.forEach((admin) => {
        participants[admin.user.id] = {
          id: admin.id,
          user: admin.user,
          imageUrl: '',
        };
      });
    }

    return participants;
  }

  async createPatientDoctorConversation({
    doctorUserId,
    patientUserId,
    prisma,
  }: {
    doctorUserId: string;
    patientUserId: string;
    prisma?: PrismaTransactionalClient;
  }): Promise<string | null> {
    return this.ensureConversationType({
      conversationType: 'patientDoctor',
      doctorUserId,
      patientUserId,
      prisma,
    }).catch((err) => {
      this.logger.error(
        err instanceof Error ? err : new Error(String(err)),
        {
          doctorUserId,
          patientUserId,
        },
        `Error ensuring conversation of type patientDoctor:`,
      );
      return null;
    });
  }

  async createDoctorAdminConversation({
    doctorUserId,
    patientUserId,
    adminUserId,
    prisma,
  }: {
    doctorUserId: string;
    patientUserId: string;
    adminUserId: string;
    prisma?: PrismaTransactionalClient;
  }): Promise<string> {
    return this.ensureConversationType({
      conversationType: 'doctorAdmin',
      doctorUserId,
      patientUserId,
      adminUserId,
      prisma,
    });
  }

  async ensureConversationType(
    params: EnsureConversationTypeParams,
  ): Promise<string> {
    const { conversationType, doctorUserId, patientUserId, prisma } = params;
    const adminUserId =
      conversationType === 'doctorAdmin' ? params.adminUserId : undefined;
    const client = prisma || this.prismaService;

    // Find the conversation by the correct unique index
    let conversation = await client.conversation.findUnique({
      where: {
        patientId_type: {
          patientId: patientUserId,
          type: conversationType,
        },
      },
    });

    if (!conversation) {
      conversation = await client.conversation.create({
        data: {
          userId: patientUserId,
          patientId: patientUserId,
          type: conversationType,
          status: conversationType === 'doctorAdmin' ? 'open' : 'active',
          assignedAdminId:
            conversationType === 'doctorAdmin' ? adminUserId : null, //If the adminDoctor convo is created by and admin, the convo will be auto-assigned to the admin
          updatedAt: null,
        },
      });
    }

    await this.ensureConversationWatchers({
      conversationId: conversation.id,
      conversationType,
      doctorUserId,
      patientUserId,
      adminUserId,
      prisma,
    });

    return conversation.id;
  }

  private async ensureConversationWatchers({
    conversationId,
    conversationType,
    doctorUserId,
    patientUserId,
    adminUserId,
    prisma,
  }: {
    conversationId: string;
    conversationType: 'patientDoctor' | 'doctorAdmin';
    doctorUserId: string;
    patientUserId: string;
    adminUserId?: string;
    prisma?: PrismaTransactionalClient;
  }): Promise<void> {
    const client = prisma || this.prismaService;
    const watchersToCreate = [
      {
        userId: doctorUserId,
        conversationId: conversationId,
      },
    ];

    if (conversationType === 'patientDoctor') {
      watchersToCreate.push({
        userId: patientUserId,
        conversationId: conversationId,
      });
    } else if (
      conversationType === 'doctorAdmin' &&
      typeof adminUserId === 'string'
    ) {
      watchersToCreate.push({
        userId: adminUserId,
        conversationId: conversationId,
      });
    }

    const existingWatchers = await client.conversationWatcher.findMany({
      where: {
        conversationId: conversationId,
        userId: { in: watchersToCreate.map((w) => w.userId) },
      },
      select: { userId: true },
    });

    const existingUserIds = new Set(existingWatchers.map((w) => w.userId));
    const newWatchers = watchersToCreate.filter(
      (w) => !existingUserIds.has(w.userId),
    );

    if (newWatchers.length > 0) {
      await client.conversationWatcher.createMany({
        data: newWatchers,
        skipDuplicates: true,
      });
    }
  }

  /**
   * Validates conversation exists and user access
   */
  private async validateConversationAccess(
    message: SendMessageInput,
    { prisma }: ExecutionContext = { prisma: this.prismaService },
  ) {
    const conversationInfo = await prisma.conversation.findFirst({
      where: { id: message.conversationId },
      include: {
        patient: {
          include: { user: true },
        },
      },
    });

    if (!conversationInfo) {
      throw new ConversationNotFoundError(message.conversationId);
    }

    await this.ensureAdminIsWatcherForDoctorAdminConversation(
      conversationInfo.type,
      message.role,
      message.conversationId,
      message.userId,
      { prisma },
    );

    const member = await this.getConversationMember(
      message.conversationId,
      message.userId,
      { prisma },
    );

    if (!member) {
      throw new UserNotConversationMemberError();
    }

    return { conversationInfo, member };
  }

  /**
   * Processes message creation and routing logic
   */
  private async processMessageInTransaction(
    message: SendMessageInput,
    conversationInfo: ConversationWithPatient,
    member: ConversationMember,
    { prisma }: ExecutionContext = { prisma: this.prismaService },
  ) {
    return runInDbTransaction(prisma, async (prisma) => {
      const newMessage = await this.createMessage(message, { prisma });

      if (message.role === roles.Patient) {
        // Patient messages in patient-doctor conversations go through message router
        await this.patientMessageRouterService.flagForProcessing(
          newMessage.conversationId,
        );
      } else {
        await this.updateConversationWatchers(message);
        if (
          conversationInfo.type === 'doctorAdmin' &&
          message.role === roles.Admin
        ) {
          await this.autoAssignConversationToAdmin(conversationInfo, message, {
            prisma,
          });
        }
      }

      await this.handleNeedsReply(message, { prisma });
      let newStatus: ConversationStatus;
      if (
        conversationInfo.type === 'doctorAdmin' &&
        (message.role === roles.Admin || message.role === roles.Doctor)
      ) {
        newStatus = 'active';
      }
      const conversation = await this.updateConversation(
        { prisma },
        message,
        newStatus,
      );

      // Log audit trail
      await this.auditService.append(
        {
          patientId: conversation.patientId,
          action: 'CONVERSATION_CHAT_MESSAGE_CREATED',
          actorType: member.user.type.toUpperCase() as AuditLog['actorType'],
          actorId:
            member.user.doctor?.id ??
            member.user.patient?.id ??
            member.user.admin?.id,
          resourceType: 'CONVERSATION',
          resourceId: message.conversationId,
          details: {
            userId: message.userId,
            type: message.type,
            contentType: message.contentType,
            content: message.content,
          },
        },
        { prisma },
      );

      return newMessage;
    });
  }

  /**
   * Handles event emissions for messages
   */
  private async handleMessageEvents(
    conversationInfo: ConversationWithPatient,
    message: SendMessageInput,
    record: CreateMessageReturnType,
    ctx?: ExecutionContext,
  ) {
    const watchers = await this.getConversationWatchers(
      message.conversationId,
      message.type === 'doctorNote' ? 'doctor' : undefined,
      ctx,
    );

    // Emit tracking events based on conversation type
    if (
      conversationInfo.type === 'patientDoctor' &&
      message.type !== 'doctorNote'
    ) {
      await this.emitPatientDoctorTrackingEvent(watchers, message, ctx);
    }
    if (conversationInfo.type === 'doctorAdmin') {
      await this.emitAdminDoctorTrackingEvent(
        conversationInfo,
        watchers,
        message,
        ctx,
      );
    }

    // Emit chat events for real-time updates
    await this.emitChatEvents(watchers, record, message);
  }

  async sendMessage(
    message: SendMessageInput,
    context: ExecutionContext = { prisma: this.prismaService },
  ) {
    // 1. Validate conversation and user access
    const { conversationInfo, member } = await this.validateConversationAccess(
      message,
      context,
    );

    return runInDbTransaction(context.prisma, async (prisma) => {
      // 2. Process message creation in transaction
      const record = await this.processMessageInTransaction(
        message,
        conversationInfo,
        member,
        {
          prisma,
        },
      );

      // 3. Handle event emissions
      await this.handleMessageEvents(conversationInfo, message, record, {
        prisma,
      });
      return record;
    });
  }

  private async emitChatEvents(
    watchers: GetConversationWatchersReturnType,
    record: CreateMessageReturnType,
    message: SendMessageInput,
  ) {
    const userIds: string[] = watchers
      .filter((watcher) => {
        return !(
          message.type == 'doctorNote' && watcher.user.type == 'patient'
        );
      })
      .map((watcher) => watcher.user.id);

    this.orchestrationService.dispatchEvent(
      'chat.message',
      new ChatEvent(userIds, record),
    );
  }

  private async getConversationMember(
    conversationId: string,
    userId: string,
    { prisma }: ExecutionContext = { prisma: this.prismaService },
  ) {
    return prisma.conversationWatcher.findFirst({
      where: { conversationId, userId },
      include: {
        user: {
          include: {
            doctor: {
              select: {
                id: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
            patient: {
              select: {
                id: true,
                idPhoto: true,
                facePhoto: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
            admin: {
              select: {
                id: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
          },
        },
      },
    });
  }

  private async createMessage(
    message: SendMessageInput,
    { prisma }: ExecutionContext = { prisma: this.prismaService },
  ) {
    const messageId: string = uuidv4();
    let messageContent = message.content;
    if (['file', 'image'].includes(message.contentType)) {
      const { destinationKey } =
        await this.chatImageService.moveFileToPermanentStorage(
          message.conversationId,
          messageId,
          message.content,
        );
      messageContent = destinationKey;
    }

    // Get the conversation to find the patient ID
    const conversation = await prisma.conversation.findFirst({
      where: { id: message.conversationId },
      select: { patientId: true },
    });

    if (!conversation) {
      throw new ConversationNotFoundError(message.conversationId);
    }

    // Check for active core treatment
    let treatmentId: string | undefined;
    const activeTreatment =
      await this.treatmentService.getActiveTreatmentByPatientId(
        conversation.patientId,
        true,
        { prisma },
      );

    // Check if treatment meets criteria
    if (
      activeTreatment &&
      activeTreatment.isCore &&
      (activeTreatment.status.startsWith('inProgress.') ||
        activeTreatment.status === 'failed' ||
        activeTreatment.status === 'paused')
    ) {
      treatmentId = activeTreatment.id;
    }

    return prisma.conversationMessage.create({
      data: {
        id: messageId,
        content: messageContent,
        conversationId: message.conversationId,
        userId: message.type === 'message' ? message.userId : null,
        contentType: message.contentType,
        type: message.type,
        ...(treatmentId && { treatmentId }),
      },
    });
  }

  private async updateConversationWatchers(
    message: SendMessageInput,
    { prisma }: ExecutionContext = { prisma: this.prismaService },
  ) {
    //1. increase the unreadMessages counter for the other convo members
    // Handle doctor note messages differently
    if (message.type === 'doctorNote') {
      // Update all doctor watchers (except the sender) in a single query
      await prisma.conversationWatcher.updateMany({
        where: {
          conversationId: message.conversationId,
          user: { type: 'doctor' },
          userId: { not: message.userId },
        },
        data: { unreadMessages: { increment: 1 }, updatedAt: new Date() },
      });
      return;
    }

    // Regular message handling (non-doctorNote)
    await prisma.conversationWatcher.updateMany({
      where: {
        conversationId: message.conversationId,
        userId: { not: message.userId },
      },
      data: { unreadMessages: { increment: 1 }, updatedAt: new Date() },
    });

    //2. reset the unreadMessages counter for the sender
    if (message.type == 'system' && message.role == roles.Doctor) return;

    await prisma.conversationWatcher.updateMany({
      where: { conversationId: message.conversationId, userId: message.userId },
      data: { unreadMessages: 0, updatedAt: new Date() },
    });
  }

  private async handleNeedsReply(
    message: SendMessageInput,
    { prisma }: ExecutionContext = { prisma: this.prismaService },
  ) {
    if (message.needsReply === undefined) return;

    if (message.role === roles.Doctor && message.needsReply) {
      await this.updateNeedsReply(message, true, { prisma });
    } else if (message.role === roles.Patient) {
      await this.updateNeedsReply(message, false, { prisma });
    }
  }

  async updateNeedsReply(
    message: SendMessageInput,
    needsReply: boolean,
    { prisma }: ExecutionContext = { prisma: this.prismaService },
  ) {
    return runInDbTransaction(prisma, async (prisma) => {
      await prisma.conversationWatcher.updateMany({
        where: {
          conversationId: message.conversationId,
          userId:
            message.role == roles.Doctor
              ? { not: message.userId }
              : message.userId,
        },
        data: { needsReply },
      });

      const conversation = await prisma.conversation.findFirst({
        where: { id: message.conversationId },
        select: { patientId: true },
      });

      if (!conversation) {
        throw new ConversationNotFoundError(message.conversationId);
      }

      await this.segment.identify(
        conversation.patientId,
        { traits: { needsReply } },
        { prisma },
      );
    });
  }

  private async updateConversation(
    { prisma }: ExecutionContext = { prisma: this.prismaService },
    message: SendMessageInput,
    status?: ConversationStatus,
  ) {
    const lastMessageText =
      message.contentType === 'text' ? message.content : 'Attachment';

    return prisma.conversation.update({
      where: { id: message.conversationId },
      data: {
        status,
        lastMessageText,
        lastMessageFrom: message.type === 'message' ? message.userId : null,
        updatedAt: new Date(),
      },
    });
  }

  private async getConversationWatchers(
    conversationId: string,
    type?: 'doctor' | 'patient',
    ctx?: ExecutionContext,
  ) {
    const prisma = ctx?.prisma || this.prismaService;

    return prisma.conversationWatcher.findMany({
      where: {
        conversationId,
        ...(type ? { user: { type } } : {}),
      },
      include: {
        user: {
          include: {
            doctor: {
              select: {
                id: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
            patient: {
              select: {
                id: true,
                doctorId: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
            admin: {
              select: {
                id: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
          },
        },
      },
    });
  }

  private async emitPatientDoctorTrackingEvent(
    watchers: GetConversationWatchersReturnType,
    message: SendMessageInput,
    ctx?: ExecutionContext,
  ) {
    const patientWatcher = watchers.find((watcher) => watcher.user.patient?.id);
    const doctor = watchers.find(
      (watcher) =>
        patientWatcher.user.patient?.doctorId &&
        watcher.user.doctor?.id &&
        patientWatcher.user.patient?.doctorId === watcher.user.doctor?.id,
    );

    if (!doctor.user || !patientWatcher.user)
      throw new Error('Doctor or patient not found in conversation watchers.');

    await this.segment.track(
      patientWatcher.user.id,
      message.role === roles.Doctor
        ? 'DoctorSentMessage'
        : 'PatientSentMessage',
      {
        properties: {
          doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
          doctorID: doctor.user.doctor.id,
          patientName: `${patientWatcher.user.firstName} ${patientWatcher.user.lastName}`,
          patientID: patientWatcher.user.patient.id,
          type: message.type === 'system' ? 'auto' : 'manual',
        },
      },
      ctx,
    );
  }

  private async emitAdminDoctorTrackingEvent(
    conversationInfo: Conversation & { patient?: Patient & { user: User } },
    watchers: GetConversationWatchersReturnType,
    message: SendMessageInput,
    ctx?: ExecutionContext,
  ) {
    // Only fire for doctorAdmin conversations and admin role
    if (message.role !== roles.Admin && message.role !== roles.Doctor) return;
    // patients have no watchers in admindoctor conversations
    const patientWatcher = conversationInfo.patient;
    const doctorWatcher = watchers.find((watcher) => watcher.user.doctor);
    const adminWatcher = watchers.find((watcher) => watcher.user.admin);
    const senderWatcher = watchers.find(
      (watcher) => watcher.user.id === message.userId,
    );

    if (!doctorWatcher?.user || !adminWatcher?.user || !patientWatcher?.user)
      return;
    await this.segment.track(
      patientWatcher.user.id,
      senderWatcher.user.type === 'admin'
        ? 'AdminMessagedDoctor'
        : 'DoctorMessagedAdmin',
      {
        properties: {
          adminName: `${adminWatcher.user.firstName} ${adminWatcher.user.lastName}`,
          doctorName: `${doctorWatcher.user.firstName} ${doctorWatcher.user.lastName}`,
          patientName: `${patientWatcher.user.firstName} ${patientWatcher.user.lastName}`,
        },
      },
      ctx,
    );
  }

  private async autoAssignConversationToAdmin(
    _conversationInfo: Conversation & {
      patient?: Patient & { user: User };
    },
    message: SendMessageInput,
    { prisma }: ExecutionContext = { prisma: this.prismaService },
  ) {
    await prisma.conversation.update({
      where: { id: message.conversationId },
      data: {
        assignedAdminId: message.userId,
        updatedAt: new Date(),
      },
    });
  }

  private async ensureAdminIsWatcherForDoctorAdminConversation(
    conversationType: ConversationType,
    userRole: SendMessageInput['role'],
    conversationId: string,
    userId: string,
    { prisma }: ExecutionContext = { prisma: this.prismaService },
  ) {
    const shouldCreateWatcher =
      conversationType == 'doctorAdmin' && userRole == roles.Admin;

    if (!shouldCreateWatcher) {
      return null;
    }

    const watcher = await prisma.conversationWatcher.findFirst({
      where: { conversationId, userId },
    });
    if (watcher) {
      return null;
    }
    return await prisma.conversationWatcher.create({
      data: {
        conversationId,
        userId,
        unreadMessages: 0,
        needsReply: false,
      },
    });
  }

  async markAsRead(conversationId: string, userId: string): Promise<null> {
    await this.prismaService.conversationWatcher.updateMany({
      where: {
        conversationId,
        userId,
      },
      data: {
        unreadMessages: 0,
        firstUnreadMessageAt: null,
      },
    });

    return null;
  }

  /**
   * Helper method to get conversation count with filters
   */
  private async getConversationCount(
    filter: AdminConversationFilter,
    adminUserId?: string,
  ): Promise<number> {
    const sevenDaysAgo = new Date(
      Date.now() - CLOSED_CONVERSATION_DAYS * 24 * 60 * 60 * 1000,
    );

    const whereClause: Prisma.ConversationWhereInput = {
      type: 'doctorAdmin',
      messages: {
        some: {},
      },
    };

    switch (filter) {
      case 'myInbox':
        whereClause.assignedAdminId = adminUserId;
        whereClause.status = { in: ['open', 'active'] };
        break;
      case 'mySent':
        whereClause.assignedAdminId = adminUserId;
        whereClause.status = { in: ['open', 'active'] };
        whereClause.lastMessageFrom = adminUserId;
        break;
      case 'myUnreplied':
        whereClause.assignedAdminId = adminUserId;
        whereClause.status = { in: ['open', 'active'] };
        whereClause.lastMessageFrom = { not: adminUserId };
        break;
      case 'allSent':
        whereClause.status = { in: ['open', 'active'] };
        whereClause.lastMessageUser = { type: 'admin' };
        break;
      case 'allUnreplied':
        whereClause.status = { in: ['open', 'active'] };
        whereClause.lastMessageUser = { type: { not: 'admin' } };
        break;
      case 'all':
        whereClause.status = { in: ['open', 'active'] };
        break;
      case 'unassigned':
        whereClause.assignedAdminId = null;
        whereClause.status = { in: ['open', 'active'] };
        break;
      case 'closed':
        whereClause.status = 'closed';
        whereClause.closedAt = {
          gte: sevenDaysAgo,
        };
        break;
    }

    return this.prismaService.readReplica().conversation.count({
      where: whereClause,
    });
  }

  async getDoctorAdminConversationsGroupCounts(adminUserId: string) {
    const [
      myInbox,
      mySent,
      myUnreplied,
      allSent,
      allUnreplied,
      all,
      unassigned,
      closed,
    ] = await Promise.all([
      this.getConversationCount('myInbox', adminUserId),
      this.getConversationCount('mySent', adminUserId),
      this.getConversationCount('myUnreplied', adminUserId),
      this.getConversationCount('allSent'),
      this.getConversationCount('allUnreplied'),
      this.getConversationCount('all'),
      this.getConversationCount('unassigned'),
      this.getConversationCount('closed'),
    ]);

    return {
      myInbox,
      mySent,
      myUnreplied,
      allSent,
      allUnreplied,
      all,
      unassigned,
      closed,
    };
  }

  async getDoctorAdminConversationMessages(conversationId: string) {
    const conversation = await this.prismaService.conversation.findFirst({
      where: {
        id: conversationId,
        type: 'doctorAdmin',
      },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                type: true,
              },
            },
          },
        },
        watcher: {
          select: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                type: true,
              },
            },
          },
        },
        patient: {
          include: {
            user: true,
            doctor: { include: { user: true } },
          },
        },
        assignedAdmin: {
          select: { id: true, firstName: true, lastName: true },
        },
      },
    });

    const doctorIds = conversation.watcher
      .filter((w) => w.user.type === 'doctor')
      .map((w) => w.user.id);

    const adminIds = conversation.watcher
      .filter((w) => w.user.type === 'admin')
      .map((w) => w.user.id);

    const [doctors, admins] = await Promise.all([
      this.prismaService.doctor.findMany({
        where: { userId: { in: doctorIds } },
        select: {
          id: true,
          image: true,
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              type: true,
            },
          },
        },
      }),
      this.prismaService.user.findMany({
        where: { id: { in: adminIds }, type: 'admin' },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          type: true,
        },
      }),
    ]);

    const participants = this.buildParticipants(
      doctors,
      [],
      admins.map((admin) => ({ id: admin.id, user: admin })),
    );

    return {
      ...conversation,
      participants,
    };
  }

  async getConversationWatcher(conversationId: string, userId: string) {
    return this.prismaService.conversationWatcher.findFirstOrThrow({
      where: {
        conversationId: conversationId,
        userId,
      },
    });
  }

  async getConversationMessages(conversationId: string, userId: string) {
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: { type: true },
    });

    const isPatient = user?.type === 'patient';

    const conversation = await this.prismaService.conversation.findFirstOrThrow(
      {
        where: {
          id: conversationId,
          OR: [{ watcher: { some: { userId } } }],
        },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
            ...(isPatient && {
              where: { type: { not: 'doctorNote' } },
            }),
            include: {
              conversationRouter: {
                select: {
                  id: true,
                  relevantForDoctor: true,
                  relevantForPatientServices: true,
                },
              },
            },
          },
          watcher: {
            select: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  type: true,
                },
              },
            },
          },
        },
      },
    );

    const doctorIds = conversation.watcher
      .filter((user) => user.user.type === 'doctor')
      .map((user) => user.user.id);
    const patientIds = conversation.watcher
      .filter((user) => user.user.type === 'patient')
      .map((user) => user.user.id);

    const doctors = await this.prismaService.doctor.findMany({
      where: { userId: { in: doctorIds } },
      select: {
        id: true,
        image: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            type: true,
          },
        },
      },
    });

    const patients = await this.prismaService.patient.findMany({
      where: { userId: { in: patientIds } },
      select: {
        id: true,
        facePhoto: true,
        user: {
          select: { id: true, firstName: true, type: true, lastName: true },
        },
      },
    });

    const participants = this.buildParticipants(doctors, patients);

    return {
      ...conversation,
      participants,
    };
  }

  async getAdminConversations({
    adminUserId,
    filter,
    page = 1,
    limit = DEFAULT_PAGE_SIZE,
  }: {
    adminUserId: string;
    filter: AdminConversationFilter;
    page?: number;
    limit?: number;
  }) {
    const admin = await this.prismaService.user.findFirst({
      where: {
        id: adminUserId,
        type: 'admin',
      },
      include: {
        admin: true,
      },
    });

    if (!admin || !admin.admin) {
      throw new AdminNotFoundError();
    }

    const whereClause: Prisma.ConversationWhereInput = {
      type: 'doctorAdmin',
      messages: {
        some: {},
      },
    };

    switch (filter) {
      case 'myInbox':
        whereClause.assignedAdminId = adminUserId;
        whereClause.status = { in: ['open', 'active'] };
        break;

      case 'mySent':
        whereClause.assignedAdminId = adminUserId;
        whereClause.status = { in: ['open', 'active'] };
        whereClause.lastMessageFrom = adminUserId;
        break;

      case 'myUnreplied':
        whereClause.assignedAdminId = adminUserId;
        whereClause.status = { in: ['open', 'active'] };
        whereClause.lastMessageFrom = { not: adminUserId };
        break;

      case 'allSent':
        whereClause.status = { in: ['open', 'active'] };
        whereClause.lastMessageUser = { type: 'admin' };
        break;

      case 'allUnreplied':
        whereClause.status = { in: ['open', 'active'] };
        whereClause.lastMessageUser = { type: { not: 'admin' } };
        break;

      case 'all':
        whereClause.status = { in: ['open', 'active'] };
        break;

      case 'unassigned':
        whereClause.assignedAdminId = null;
        whereClause.status = { in: ['open', 'active'] };
        break;

      case 'closed':
        whereClause.status = 'closed';
        whereClause.closedAt = {
          gte: new Date(
            Date.now() - CLOSED_CONVERSATION_DAYS * 24 * 60 * 60 * 1000,
          ),
        };
        break;
    }

    const skip = (page - 1) * limit;

    const [conversations, totalCount] = await Promise.all([
      this.prismaService.conversation.findMany({
        where: whereClause,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      id: true,
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          },
          assignedAdmin: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 1,
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  type: true,
                },
              },
            },
          },
          watcher: {
            where: { userId: adminUserId },
            select: {
              unreadMessages: true,
              updatedAt: true,
            },
          },
        },
        orderBy: [{ updatedAt: 'desc' }, { createdAt: 'desc' }],
        skip,
        take: limit,
      }),
      this.prismaService.conversation.count({
        where: whereClause,
      }),
    ]);

    return {
      conversation: conversations.map((conversation) => ({
        id: conversation.id,
        patientId: conversation.patientId,
        status: conversation.status,
        assignedAdminId: conversation.assignedAdminId,
        lastMessageText: conversation.lastMessageText,
        createdAt: conversation.createdAt,
        updatedAt: conversation.updatedAt,
        closedAt: conversation.closedAt,
        patient: {
          id: conversation.patient.id,
          facePhoto: conversation.patient.facePhoto,
          user: conversation.patient.user,
          doctor: conversation.patient.doctor,
        },
        assignedAdmin: conversation.assignedAdmin,
        lastMessage: conversation.messages[0] || null,
        unreadMessages: conversation.watcher[0]?.unreadMessages || 0,
      })),
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        hasNextPage: page * limit < totalCount,
        hasPreviousPage: page > 1,
      },
    };
  }
}
