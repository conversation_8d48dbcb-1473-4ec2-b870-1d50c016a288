import { writeFileSync } from 'fs';
import type {
  Patient,
  PatientShippingAddress,
  State,
  User,
} from '@prisma/client';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { RedRockPharmacyService } from '@modules/integrations/pharmacy/services/redrock.pharmacy.service';
import { Injectable } from '@nestjs/common';
import { Command, CommandRunner, Option } from 'nest-commander';

type PatientWithRelations = Patient & {
  user: User;
  state: State;
  shippingAddresses: (PatientShippingAddress & {
    state: State;
  })[];
};

interface AddressComparison {
  patient: PatientWithRelations;
  redRockStore: 'springville' | 'stgeorge';
  redRockPatientId: string;
  localAddress: {
    street1: string;
    street2?: string;
    city: string;
    state: string;
    zipCode: string;
  };
  redRockAddress?: {
    street1: string;
    street2?: string;
    city: string;
    state: string;
    zipCode: string;
  };
  needsUpdate: boolean;
  differences: string[];
  error?: string;
}

@Injectable()
@Command({
  name: 'sync-redrock-addresses',
  description:
    'Sync patient addresses between local database and Red Rock pharmacy API',
})
export class SyncRedRockAddressesCommand extends CommandRunner {
  constructor(
    private readonly prisma: PrismaService,
    private readonly redRockService: RedRockPharmacyService,
  ) {
    super();
  }

  @Option({
    flags: '--dry-run',
    description: 'Show what would be updated without actually making changes',
  })
  parseDryRun(): boolean {
    return true;
  }

  @Option({
    flags: '--limit [number]',
    description: 'Limit the number of patients to process',
  })
  parseLimit(val: string): number {
    const limit = parseInt(val, 10);
    if (isNaN(limit) || limit < 1) {
      throw new Error('Limit must be a positive number');
    }
    return limit;
  }

  @Option({
    flags: '--offset [number]',
    description: 'Skip the first N patients (for pagination, default: 0)',
  })
  parseOffset(val: string): number {
    const offset = parseInt(val, 10);
    if (isNaN(offset) || offset < 0) {
      throw new Error('Offset must be a non-negative number');
    }
    return offset;
  }

  @Option({
    flags: '--delay [number]',
    description: 'Delay in milliseconds between API calls (default: 500ms)',
  })
  parseDelay(val: string): number {
    const delay = parseInt(val, 10);
    if (isNaN(delay) || delay < 0) {
      throw new Error('Delay must be a non-negative number');
    }
    return delay;
  }

  @Option({
    flags: '--concurrency [number]',
    description:
      'Number of concurrent API calls for fetching data (default: 5)',
  })
  parseConcurrency(val: string): number {
    const concurrency = parseInt(val, 10);
    if (isNaN(concurrency) || concurrency < 1) {
      throw new Error('Concurrency must be a positive number');
    }
    return concurrency;
  }

  @Option({
    flags: '--state [state]',
    description: 'Filter patients by state code (e.g., UT, CA, NV)',
  })
  parseState(val: string): string {
    return val.toUpperCase();
  }

  /**
   * Helper function to create a delay
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Compare two addresses and return differences
   */
  private compareAddresses(
    local: AddressComparison['localAddress'],
    redRock: AddressComparison['redRockAddress'],
  ): { needsUpdate: boolean; differences: string[] } {
    if (!redRock) {
      return {
        needsUpdate: false,
        differences: ['Red Rock address not found'],
      };
    }

    const differences: string[] = [];

    // Normalize strings for comparison (trim, handle undefined)
    const normalize = (str?: string) => (str || '').trim().toLowerCase();

    // Concatenate local street1 and street2 to match Red Rock's format
    const localFullAddress = `${local.street1}${local.street2 ? ' ' + local.street2 : ''}`;
    const redRockFullAddress = redRock.street1; // Red Rock stores as single address in street1

    if (normalize(localFullAddress) !== normalize(redRockFullAddress)) {
      differences.push(
        `Address: "${localFullAddress}" vs "${redRockFullAddress}"`,
      );
    }

    if (normalize(local.city) !== normalize(redRock.city)) {
      differences.push(`City: "${local.city}" vs "${redRock.city}"`);
    }

    if (normalize(local.state) !== normalize(redRock.state)) {
      differences.push(`State: "${local.state}" vs "${redRock.state}"`);
    }

    // Normalize zip codes (remove dashes, spaces, take first 5 digits)
    const normalizeZip = (zip?: string) =>
      (zip || '').replace(/[-\s]/g, '').substring(0, 5);
    if (normalizeZip(local.zipCode) !== normalizeZip(redRock.zipCode)) {
      differences.push(`Zip: "${local.zipCode}" vs "${redRock.zipCode}"`);
    }

    return {
      needsUpdate: differences.length > 0,
      differences,
    };
  }

  /**
   * Fetch patients with Red Rock IDs
   */
  private async fetchPatientsWithRedRockIds(
    stateFilter?: string,
    limit?: number,
    offset?: number,
  ): Promise<PatientWithRelations[]> {
    const whereClause: any = {
      OR: [
        { redrockSpringvillePatientId: { not: null } },
        { redrockStGeorgePatientId: { not: null } },
      ],
    };

    if (stateFilter) {
      whereClause.state = { code: stateFilter };
    }

    return this.prisma.patient.findMany({
      where: whereClause,
      include: {
        user: true,
        state: true,
        shippingAddresses: {
          include: { state: true },
          where: { default: true },
          take: 1,
        },
      },
      take: limit,
      skip: offset,
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Fetch patient data from Red Rock API with concurrency control
   */
  private async fetchRedRockData(
    patients: PatientWithRelations[],
    concurrency: number,
  ): Promise<AddressComparison[]> {
    const results: AddressComparison[] = [];
    const batches = [];

    // Create batches for concurrency control
    for (let i = 0; i < patients.length; i += concurrency) {
      batches.push(patients.slice(i, i + concurrency));
    }

    console.log(
      `Processing ${patients.length} patients in ${batches.length} batches of ${concurrency}`,
    );

    for (const [batchIndex, batch] of batches.entries()) {
      console.log(`Processing batch ${batchIndex + 1}/${batches.length}...`);

      const batchPromises = batch.map(async (patient) => {
        const defaultAddress = patient.shippingAddresses[0];
        if (!defaultAddress) {
          return {
            patient,
            redRockStore: 'springville' as const,
            redRockPatientId: '',
            localAddress: {
              street1: '',
              street2: undefined,
              city: '',
              state: '',
              zipCode: '',
            },
            needsUpdate: false,
            differences: ['No default address found'],
            error: 'No default address found',
          } as AddressComparison;
        }

        const localAddress = {
          street1: defaultAddress.address1,
          street2: defaultAddress.address2 || undefined,
          city: defaultAddress.city,
          state: defaultAddress.state.code,
          zipCode: defaultAddress.zip,
        };

        // Determine Red Rock store and patient ID
        const isStGeorge = ['NV', 'CA'].includes(
          patient.state.code.toUpperCase(),
        );
        const redRockStore = isStGeorge ? 'stgeorge' : 'springville';
        const redRockPatientId = isStGeorge
          ? patient.redrockStGeorgePatientId
          : patient.redrockSpringvillePatientId;

        if (!redRockPatientId) {
          return {
            patient,
            redRockStore,
            redRockPatientId: '',
            localAddress,
            needsUpdate: false,
            differences: [`No Red Rock patient ID for ${redRockStore}`],
            error: `No Red Rock patient ID for ${redRockStore}`,
          } as AddressComparison;
        }

        // Fetch Red Rock patient data
        try {
          const redRockResponse = await this.redRockService.getPatientDetails(
            patient.id,
            patient.state.code,
          );

          if (!redRockResponse.success) {
            return {
              patient,
              redRockStore,
              redRockPatientId,
              localAddress,
              needsUpdate: false,
              differences: [],
              error: redRockResponse.error,
            } as AddressComparison;
          }

          const comparison = this.compareAddresses(
            localAddress,
            redRockResponse.data?.address,
          );

          return {
            patient,
            redRockStore,
            redRockPatientId,
            localAddress,
            redRockAddress: redRockResponse.data?.address,
            needsUpdate: comparison.needsUpdate,
            differences: comparison.differences,
          } as AddressComparison;
        } catch (error) {
          return {
            patient,
            redRockStore,
            redRockPatientId,
            localAddress,
            needsUpdate: false,
            differences: [],
            error: error.message || 'Unknown error fetching Red Rock data',
          } as AddressComparison;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Small delay between batches
      if (batchIndex < batches.length - 1) {
        await this.sleep(100);
      }
    }

    return results;
  }

  /**
   * Update patient addresses in Red Rock
   */
  private async updatePatientAddresses(
    patientsToUpdate: AddressComparison[],
    delay: number,
    isDryRun: boolean,
  ): Promise<{
    success: number;
    failed: number;
    csvRecords: Array<{
      patientId: string;
      name: string;
      status: string;
      error?: string;
    }>;
  }> {
    let success = 0;
    let failed = 0;
    const csvRecords: Array<{
      patientId: string;
      name: string;
      status: string;
      error?: string;
    }> = [];

    console.log(
      `${isDryRun ? '[DRY RUN] ' : ''}Updating ${patientsToUpdate.length} patient addresses...`,
    );

    for (const [index, comparison] of patientsToUpdate.entries()) {
      const { patient, localAddress } = comparison;
      const patientName = `${patient.user.firstName} ${patient.user.lastName}`;

      try {
        console.log(
          `${isDryRun ? '[DRY RUN] ' : ''}[${index + 1}/${patientsToUpdate.length}] Processing ${patientName}...`,
        );

        if (isDryRun) {
          console.log(
            `  Would update address: ${localAddress.street1}, ${localAddress.city}, ${localAddress.state} ${localAddress.zipCode}`,
          );
          console.log(`  Differences: ${comparison.differences.join('; ')}`);
          success++;
          csvRecords.push({
            patientId: patient.id,
            name: patientName,
            status: 'would_update',
          });
        } else {
          const updateResult = await this.redRockService.updatePatient(
            patient.id,
            { address: localAddress },
            patient.state.code,
          );

          if (updateResult) {
            console.log(`  ✅ Successfully updated ${patientName}`);
            success++;
            csvRecords.push({
              patientId: patient.id,
              name: patientName,
              status: 'updated',
            });
          } else {
            console.warn(`  ❌ Failed to update ${patientName}`);
            failed++;
            csvRecords.push({
              patientId: patient.id,
              name: patientName,
              status: 'failed',
              error: 'Update returned false',
            });
          }
        }

        // Add delay between updates (skip on last item)
        if (index < patientsToUpdate.length - 1) {
          await this.sleep(delay);
        }
      } catch (error) {
        console.error(`  ❌ Error updating ${patientName}: ${error.message}`);
        failed++;
        csvRecords.push({
          patientId: patient.id,
          name: patientName,
          status: 'failed',
          error: error.message || 'Unknown error',
        });
      }
    }

    return { success, failed, csvRecords };
  }

  async run(
    _: string[],
    options?: {
      dryRun?: boolean;
      limit?: number;
      offset?: number;
      delay?: number;
      concurrency?: number;
      state?: string;
    },
  ): Promise<void> {
    const isDryRun = options?.dryRun || false;
    const limit = options?.limit;
    const offset = options?.offset || 0;
    const delay = options?.delay || 500;
    const concurrency = options?.concurrency || 5;
    const stateFilter = options?.state;

    console.log('🔍 Starting Red Rock address sync...');

    if (isDryRun) {
      console.log('🏃‍♂️ Running in DRY RUN mode - no changes will be made');
    }

    if (stateFilter) {
      console.log(`📍 Filtering by state: ${stateFilter}`);
    }

    if (offset > 0) {
      console.log(`⏩ Starting from offset: ${offset}`);
    }

    if (limit) {
      console.log(`📏 Processing up to ${limit} patients`);
    }

    // Step 1: Fetch patients with Red Rock IDs
    console.log('\n📋 Fetching patients with Red Rock IDs...');
    const patients = await this.fetchPatientsWithRedRockIds(
      stateFilter,
      limit,
      offset,
    );

    if (patients.length === 0) {
      console.log('✅ No patients found with Red Rock IDs');
      return;
    }

    console.log(`Found ${patients.length} patients with Red Rock IDs`);

    // Step 2: Fetch Red Rock data and compare addresses
    console.log(
      '\n🔄 Fetching Red Rock patient data and comparing addresses...',
    );
    const comparisons = await this.fetchRedRockData(patients, concurrency);

    // Analyze results
    const patientsWithErrors = comparisons.filter((c) => c.error);
    const patientsNeedingUpdates = comparisons.filter(
      (c) => c.needsUpdate && !c.error,
    );
    const patientsInSync = comparisons.filter(
      (c) => !c.needsUpdate && !c.error,
    );

    console.log('\n📊 Analysis Results:');
    console.log(`  ✅ Patients in sync: ${patientsInSync.length}`);
    console.log(
      `  🔄 Patients needing updates: ${patientsNeedingUpdates.length}`,
    );
    console.log(`  ❌ Patients with errors: ${patientsWithErrors.length}`);

    // Log patients with errors
    if (patientsWithErrors.length > 0) {
      console.warn('\n❌ Patients with errors:');
      patientsWithErrors.forEach((comparison) => {
        const name = `${comparison.patient.user.firstName} ${comparison.patient.user.lastName}`;
        console.warn(`  ${name}: ${comparison.error}`);
      });
    }

    // Log patients needing updates
    if (patientsNeedingUpdates.length > 0) {
      console.log('\n🔄 Patients needing address updates:');
      patientsNeedingUpdates.forEach((comparison) => {
        const name = `${comparison.patient.user.firstName} ${comparison.patient.user.lastName}`;
        console.log(`  ${name} (${comparison.redRockStore}):`);
        comparison.differences.forEach((diff) => {
          console.log(`    - ${diff}`);
        });
      });
    }

    // Step 3: Update patient addresses
    if (patientsNeedingUpdates.length > 0) {
      console.log(
        `\n🚀 ${isDryRun ? '[DRY RUN] ' : ''}Updating patient addresses...`,
      );

      const updateResults = await this.updatePatientAddresses(
        patientsNeedingUpdates,
        delay,
        isDryRun,
      );

      // Final summary
      console.log(`\n🎯 ${isDryRun ? 'Dry run' : 'Sync'} complete:`);
      console.log(
        `  ✅ ${isDryRun ? 'Would update' : 'Successfully updated'}: ${updateResults.success} patients`,
      );

      if (!isDryRun && updateResults.failed > 0) {
        console.log(`  ❌ Failed to update: ${updateResults.failed} patients`);
      }

      // Generate CSV report
      if (updateResults.csvRecords.length > 0) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const csvFilename = `redrock-address-sync-${isDryRun ? 'dry-run-' : ''}${timestamp}.csv`;

        const csvContent = [
          'Patient ID,Patient Name,Status,Error',
          ...updateResults.csvRecords.map(
            (record) =>
              `${record.patientId},"${record.name}",${record.status},"${record.error || ''}"`,
          ),
        ].join('\n');

        try {
          writeFileSync(csvFilename, csvContent);
          console.log(`\n📄 Report generated: ${csvFilename}`);
        } catch (error) {
          console.error(`\n❌ Failed to write CSV report: ${error.message}`);
        }
      }
    } else {
      console.log(
        '\n✅ No patients need address updates - all addresses are in sync!',
      );
    }
  }
}
