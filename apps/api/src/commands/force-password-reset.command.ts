import confirm from '@inquirer/confirm';
import search from '@inquirer/search';
import { CognitoService, Roles } from '@modules/auth/cognito.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import * as chalk from 'chalk';
import { Command, CommandRunner, Option } from 'nest-commander';

interface UserOption {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  type: 'admin' | 'doctor';
  displayName: string;
}

@Injectable()
@Command({
  name: 'force-password-reset',
  description:
    'Force password reset for admins and doctors by invalidating current password and sending recovery email',
})
export class ForcePasswordResetCommand extends CommandRunner {
  private readonly logger = new Logger(ForcePasswordResetCommand.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly cognitoService: CognitoService,
  ) {
    super();
  }

  @Option({
    flags: '--email [email]',
    description: 'User email to force reset (skips user selection)',
  })
  parseEmail(val: string): string {
    return val;
  }

  @Option({
    flags: '--role [role]',
    description: 'User role: Admin or Doctor (required with --email)',
  })
  parseRole(val: string): Roles {
    const normalized = val.charAt(0).toUpperCase() + val.slice(1).toLowerCase();
    if (normalized !== 'Admin' && normalized !== 'Doctor') {
      throw new Error('Role must be either "Admin" or "Doctor"');
    }
    return normalized as Roles;
  }

  async run(
    _: string[],
    options?: {
      email?: string;
      role?: Roles;
    },
  ): Promise<void> {
    try {
      const isInteractive = !options?.email;

      // Validate that if email is provided, role must also be provided
      if (options?.email && !options?.role) {
        throw new Error(
          'When using --email flag, --role flag is also required (Admin or Doctor)',
        );
      }

      // 1. Get user selection
      const selectedUser = options?.email
        ? await this.getUserByEmail(options.email, options.role!)
        : await this.selectUser();

      // 2. Show confirmation (skip if both flags provided)
      const needsConfirmation = !options?.email || !options?.role;
      if (needsConfirmation) {
        const confirmed = await this.showConfirmation(selectedUser);

        if (!confirmed) {
          console.log(chalk.yellow('Operation cancelled.'));
          return;
        }
      }

      // 3. Execute password reset
      await this.executePasswordReset(selectedUser);

      // 4. Show success message and non-interactive command if run interactively
      if (isInteractive) {
        console.log(
          chalk.green(
            `\n✅ Successfully forced password reset for ${selectedUser.email}`,
          ),
        );
        console.log(
          chalk.gray(
            `   - Current password invalidated\n   - All sessions signed out\n   - Password recovery email sent`,
          ),
        );
        this.showNonInteractiveCommand(selectedUser);
      }
    } catch (error) {
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  }

  private async selectUser(): Promise<UserOption> {
    console.log(chalk.blue('Loading admin and doctor users...'));

    const users = await this.prismaService.user.findMany({
      where: {
        type: {
          in: ['admin', 'doctor'],
        },
        deletedAt: null,
      },
      orderBy: [{ type: 'asc' }, { email: 'asc' }],
    });

    if (users.length === 0) {
      throw new Error('No admin or doctor users found in the database');
    }

    const userOptions: UserOption[] = users.map((user) => ({
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      type: user.type as 'admin' | 'doctor',
      displayName: `${user.type.toUpperCase()} | ${user.firstName} ${user.lastName} | ${user.email}`,
    }));

    const selectedUserId = await search({
      message: 'Select a user to force password reset (type to search):',
      source: async (input: string) => {
        const filtered = userOptions.filter((option) =>
          option.displayName.toLowerCase().includes(input?.toLowerCase() || ''),
        );
        return filtered.map((option) => ({
          name: option.displayName,
          value: option.id,
        }));
      },
    });

    const selectedUser = userOptions.find((u) => u.id === selectedUserId);
    if (!selectedUser) {
      throw new Error('Selected user not found');
    }

    return selectedUser;
  }

  private async getUserByEmail(
    email: string,
    role: Roles,
  ): Promise<UserOption> {
    const userType = role.toLowerCase() as 'admin' | 'doctor';

    const user = await this.prismaService.user.findFirst({
      where: {
        email,
        type: userType,
        deletedAt: null,
      },
    });

    if (!user) {
      throw new Error(
        `${role} user with email ${email} not found or has been deleted`,
      );
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      type: userType,
      displayName: `${user.type.toUpperCase()} | ${user.firstName} ${user.lastName} | ${user.email}`,
    };
  }

  private async showConfirmation(user: UserOption): Promise<boolean> {
    console.log('\n' + chalk.cyan('=== CONFIRMATION ==='));
    console.log(`${chalk.bold('User:')} ${user.displayName}`);
    console.log(`${chalk.bold('Email:')} ${user.email}`);
    console.log(
      `${chalk.bold('Type:')} ${user.type.charAt(0).toUpperCase() + user.type.slice(1)}`,
    );
    console.log('\n' + chalk.yellow('This action will:'));
    console.log(
      '  1. Invalidate current password (user cannot sign in with it)',
    );
    console.log(
      '  2. Sign out all active sessions (invalidate refresh tokens)',
    );
    console.log('  3. Send password recovery email to user');
    console.log('\n');

    return await confirm({
      message: `Force password reset for ${user.email}?`,
      default: false,
    });
  }

  private async executePasswordReset(user: UserOption): Promise<void> {
    console.log(chalk.blue('\nExecuting password reset...'));

    try {
      // Step 1: Set random temporary password to invalidate current one
      console.log(chalk.gray('1. Invalidating current password...'));
      await this.cognitoService.overridePassword(
        user.email,
        Math.random().toString(36) + Math.random().toString(36),
      );

      // Step 2: Sign out globally to invalidate all refresh tokens
      console.log(chalk.gray('2. Signing out all sessions...'));
      await this.cognitoService.signOutGlobally(user.email);

      // Step 3: Initiate password reset (generate token and send email)
      console.log(chalk.gray('3. Sending password recovery email...'));
      const role = (user.type.charAt(0).toUpperCase() +
        user.type.slice(1)) as Roles;
      await this.cognitoService.initiatePasswordReset(user.email, role);

      console.log(chalk.green('✓ Password reset completed successfully'));
    } catch (error) {
      throw new Error(`Failed to execute password reset: ${error.message}`);
    }
  }

  private showNonInteractiveCommand(user: UserOption): void {
    console.log('\n' + chalk.cyan('=== NON-INTERACTIVE COMMAND ==='));
    console.log(chalk.gray('To run this command non-interactively, use:'));
    console.log('');

    const role = user.type.charAt(0).toUpperCase() + user.type.slice(1);
    const command = `pnpm -F api cli force-password-reset --email "${user.email}" --role "${role}"`;

    console.log(chalk.white(command));
    console.log('');
  }
}
