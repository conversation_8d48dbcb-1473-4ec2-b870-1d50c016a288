import { useMutation } from '@tanstack/react-query';
import { useSet<PERSON>tom } from 'jotai';

import { apiClient } from '@willow/utils/api/client';

import type { SignInResponse } from '~/store/accessToken';
import { accessTokenAtom } from '~/store/accessToken';

export const useLogin = () => {
  const setSignIn = useSetAtom(accessTokenAtom);

  return useMutation<
    { data: SignInResponse },
    unknown,
    { email: string; password: string }
  >({
    mutationFn: ({ email, password }) =>
      apiClient.post(`/admin/signin`, { email, password }),
    onSuccess: ({ data }) => {
      setSignIn({
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
      });
    },
  });
};

export const useRefreshToken = () => {
  return useMutation<
    { data: SignInResponse },
    unknown,
    { refreshToken: string }
  >({
    retry: false,
    mutationFn: async (data) => {
      return await apiClient.post(`/admin/refresh`, data, {
        bypassInterceptor: true,
      });
    },
  });
};

export const useForgotPassword = () => {
  return useMutation<unknown, unknown, { email: string }>({
    mutationFn: (data) =>
      apiClient.post(`/admin/forgot-password`, data, {
        bypassInterceptor: true,
      }),
  });
};

export const useResetPassword = () => {
  return useMutation<unknown, unknown, { token: string; password: string }>({
    mutationFn: (data) =>
      apiClient.post(`/admin/reset-password`, data, {
        bypassInterceptor: true,
      }),
  });
};
