import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { apiClient } from '@willow/utils/api/client';

export interface ValueOptions {
  type: 'string' | 'number' | 'boolean' | 'json' | 'select' | 'multi-select';
  options?: { label: string; value: string | number }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface Setting {
  id: string;
  key: string;
  name: string;
  description: string | null;
  value: any;
  valueOptions: ValueOptions;
  status: 'active' | 'inactive' | 'archived';
  required: boolean;
  startDate: string | null;
  endDate: string | null;
  createdAt: string;
  updatedAt: string;
}

interface FetchParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  direction?: 'asc' | 'desc';
  status?: string;
}

interface SettingsResponse {
  data: Setting[];
  meta: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

// Get settings with pagination and search
export const useGetSettings = (params: FetchParams) => {
  return useQuery({
    queryKey: ['settings', params],
    queryFn: async () => {
      const response = await apiClient.get('/setting', {
        params,
      });
      return response.data as SettingsResponse;
    },
  });
};

// Get a single setting by ID
export const useGetSetting = (id: string) => {
  return useQuery({
    queryKey: ['setting', id],
    queryFn: async () => {
      const { data } = await apiClient.get(`/setting/${id}`);
      return data as Setting;
    },
    enabled: !!id && id !== 'new',
  });
};

// Create a new setting
export const useCreateSetting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (settingData: {
      key: string;
      name: string;
      description?: string | null;
      value: any;
      valueOptions: any;
      status?: 'active' | 'inactive' | 'archived';
      required?: boolean;
      startDate?: string | null;
      endDate?: string | null;
    }) => {
      const { data } = await apiClient.post('/setting', settingData);
      return data;
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
  });
};

// Update an existing setting
export const useUpdateSetting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: {
        name?: string;
        description?: string | null;
        value?: any;
        valueOptions?: any;
        status?: 'active' | 'inactive' | 'archived';
        required?: boolean;
        startDate?: string | null;
        endDate?: string | null;
      };
    }) => {
      const response = await apiClient.patch(`/setting/${id}`, data);
      return response.data;
    },
    onSuccess: (_, { id }) => {
      void queryClient.invalidateQueries({ queryKey: ['settings'] });
      void queryClient.invalidateQueries({ queryKey: ['setting', id] });
    },
  });
};

// Delete a setting (soft delete)
export const useDeleteSetting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete(`/setting/${id}`);
      return data;
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
  });
};
