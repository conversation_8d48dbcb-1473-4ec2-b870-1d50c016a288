'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { parseAsString, useQueryState } from 'nuqs';

import { cn } from '@willow/ui';
import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { ColumnHeader } from '@willow/ui/column-header';
import { Loader } from '@willow/ui/loader';
import { Pagination } from '@willow/ui/pagination';
import { SearchInput } from '@willow/ui/search-input';
import {
  usePaginationSearchParams,
  useSortingQueryState,
} from '@willow/utils/table';

import type { Setting } from '~/hooks/settings';
import { useGetSettings } from '~/hooks/settings';
import { SettingInfo } from './setting-info';

// Helper function to format setting values based on metadata
function formatSettingValue(setting: Setting): string {
  const { value, valueOptions } = setting;

  // Handle null/undefined values
  if (value === null || value === undefined) {
    return '-';
  }

  // Handle different value types based on valueOptions.type
  if (!valueOptions) {
    return String(value);
  }

  switch (valueOptions.type) {
    case 'select': {
      // Find the matching option and return its label
      const option = valueOptions.options?.find((opt) => opt.value === value);
      return option ? option.label : String(value);
    }

    case 'multi-select':
      if (Array.isArray(value)) {
        // Map array values to labels and join with comma
        const labels = value.map((val) => {
          const option = valueOptions.options?.find((opt) => opt.value === val);
          return option ? option.label : String(val);
        });
        return labels.join(', ');
      }
      return String(value);

    case 'boolean':
      return value ? 'True' : 'False';

    case 'number':
      return String(value);

    case 'string':
      return String(value);

    case 'json':
      return JSON.stringify(value);

    default:
      return String(value);
  }
}

// Helper function to truncate long values
function truncateValue(value: string, maxLength = 50): string {
  return value.length > maxLength
    ? value.substring(0, maxLength) + '...'
    : value;
}

export function SettingsTable() {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useQueryState(
    'query',
    parseAsString.withDefault(''),
  );
  const [pagination, setPagination] = usePaginationSearchParams();
  const [sorting, setSorting] = useSortingQueryState();

  const [selectedSettingId, setSelectedSettingId] = useQueryState('settingId', {
    defaultValue: '',
  });

  // State for showing inactive settings - show all by default
  const [showInactive, setShowInactive] = useState(true);

  const fetchParams = useMemo(
    () => ({
      sortBy: sorting.length > 0 ? sorting[0]?.id : undefined,
      direction:
        sorting.length > 0
          ? sorting[0]?.desc
            ? ('desc' as const)
            : ('asc' as const)
          : ('asc' as const),
      page: pagination.page,
      limit: pagination.pageSize,
      search: query ?? undefined,
    }),
    [query, pagination, sorting],
  );

  const { data, isPending, isError } = useGetSettings(fetchParams);

  const columns: ColumnDef<Setting>[] = useMemo(
    () => [
      {
        accessorKey: 'key',
        header: () => <ColumnHeader label="Key" sortKey="key" />,
        cell: ({ row }) => (
          <div className="text-sm font-medium">{row.original.key}</div>
        ),
      },
      {
        accessorKey: 'name',
        header: () => <ColumnHeader label="Name" sortKey="name" />,
        cell: ({ row }) => (
          <div className="text-sm font-medium">{row.original.name}</div>
        ),
      },
      {
        accessorKey: 'value',
        header: () => <ColumnHeader label="Value" sortKey="value" />,
        cell: ({ row }) => {
          const setting = row.original;
          const formattedValue = formatSettingValue(setting);
          const displayValue = truncateValue(formattedValue);

          return (
            <div className="max-w-48 truncate text-sm">{displayValue}</div>
          );
        },
      },
      {
        accessorKey: 'required',
        header: () => <ColumnHeader label="Required" sortKey="required" />,
        cell: ({ row }) => {
          const required = row.original.required;
          return (
            <span
              className={cn(
                'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                required
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800',
              )}
            >
              {required ? 'Required' : 'Not Required'}
            </span>
          );
        },
      },
      {
        accessorKey: 'status',
        header: () => <ColumnHeader label="Status" sortKey="status" />,
        cell: ({ row }) => {
          const status = row.original.status;
          const statusColors = {
            active: 'bg-green-100 text-green-800',
            inactive: 'bg-yellow-100 text-yellow-800',
            archived: 'bg-red-100 text-red-800',
          };

          return (
            <span
              className={cn(
                'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                statusColors[status] || 'bg-gray-100 text-gray-800',
              )}
            >
              {status}
            </span>
          );
        },
      },
    ],
    [],
  );

  // Filter settings based on showInactive
  const settings = useMemo(() => {
    const allSettings = data?.data ?? [];

    if (!showInactive) {
      return allSettings.filter((setting) => setting.status === 'active');
    }

    return allSettings;
  }, [data, showInactive]);

  const table = useReactTable({
    data: settings,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
    manualPagination: true,
    pageCount: data?.meta.pages ?? 0,
  });

  const handleSearchSubmit = useCallback(
    (query: string | null) => {
      void setQuery(query);
      void setPagination({ page: 1 });
    },
    [setPagination, setQuery],
  );

  return (
    <div className="grid h-[calc(100vh-96px)] w-full grid-rows-[auto_1fr_auto]">
      <div className="w-full">
        <div className="mb-5 text-2xl font-medium text-denim">Settings</div>
        <SearchInput
          className="mb-5"
          ref={searchInputRef}
          onSearch={handleSearchSubmit}
          placeholder="Search settings"
          defaultValue={query}
        />
      </div>

      <div className="relative flex-1 overflow-auto">
        <Drawer
          direction="right"
          open={selectedSettingId !== ''}
          onOpenChange={(value) => {
            if (!value) void setSelectedSettingId('');
          }}
        >
          <div className="h-full w-full">
            {isPending ? (
              <div className="flex h-40 w-full items-center justify-center">
                <Loader size="lg" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          key={header.id}
                          className="font-bold text-dark"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (
                      <DrawerTrigger
                        key={row.id}
                        asChild
                        onClick={() => {
                          void setSelectedSettingId(row.original.id);
                        }}
                      >
                        <TableRow
                          data-state={
                            selectedSettingId === row.original.id && 'selected'
                          }
                          className="h-[60px] cursor-pointer hover:bg-muted/50"
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id} className="p-2">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      </DrawerTrigger>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        {isError ? 'Error loading settings.' : 'No results.'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </div>
          {selectedSettingId && (
            <DrawerPortal>
              <DrawerOverlay className="fixed inset-0 bg-dark/40" />
              <DrawerContent className="fixed bottom-0 right-0 top-0 z-10 flex w-[800px] !touch-none !select-text">
                <DrawerTitle className="hidden">
                  Setting Information
                </DrawerTitle>
                <SettingInfo
                  settingId={selectedSettingId}
                  onClose={() => {
                    void setSelectedSettingId('');
                  }}
                />
              </DrawerContent>
            </DrawerPortal>
          )}
        </Drawer>
      </div>

      <div className={cn('flex items-center justify-between py-4')}>
        <div className="flex gap-3">
          <Button
            variant="electric"
            onClick={() => {
              setShowInactive(!showInactive);
              void setPagination({ page: 1 }); // Reset to first page when toggling
            }}
          >
            {showInactive ? 'Show Active Settings Only' : 'Show All Settings'}
          </Button>
        </div>
        {table.getRowModel().rows.length > 0 && (
          <Pagination
            currentPage={pagination.page}
            totalPages={table.getPageCount()}
            onPageChange={(page) => setPagination({ page })}
          />
        )}
      </div>
    </div>
  );
}
