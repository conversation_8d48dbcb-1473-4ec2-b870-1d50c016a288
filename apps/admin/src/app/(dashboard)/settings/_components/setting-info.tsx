'use client';

import { useEffect, useRef, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { XIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { Calendar } from '@willow/ui/base/calendar';
import { Checkbox } from '@willow/ui/base/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@willow/ui/base/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@willow/ui/base/select';
import { Switch } from '@willow/ui/base/switch';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@willow/ui/base/tabs';
import { Textarea } from '@willow/ui/base/textarea';
import { useToast } from '@willow/ui/base/use-toast';
import { Loader } from '@willow/ui/loader';

import type { Setting, ValueOptions } from '~/hooks/settings';
import {
  useDeleteSetting,
  useGetSetting,
  useUpdateSetting,
} from '~/hooks/settings';
import { SettingMiscMenu } from './setting-misc-menu';

interface SettingInfoProps {
  settingId: string;
  onClose: () => void;
}

const updateSettingSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional().nullable(),
  value: z.any(), // Changed to z.any() to support different value types
  status: z.enum(['active', 'inactive', 'archived']),
  required: z.boolean(),
  startDate: z.string().optional().nullable(),
  endDate: z.string().optional().nullable(),
});

// Helper function to render dynamic value input based on setting metadata
function renderValueField(
  field: any,
  setting: Setting,
  jsonError: string | null,
) {
  const { valueOptions } = setting;

  // Handle different value types based on valueOptions.type
  if (!valueOptions) {
    return (
      <Input
        placeholder="Enter text"
        value={field.value ?? ''}
        onChange={(e) => field.onChange(e.target.value)}
      />
    );
  }

  switch (valueOptions.type) {
    case 'multi-select': {
      // Multi-select with checkboxes
      const selectedValues = Array.isArray(field.value) ? field.value : [];

      return (
        <div className="space-y-2">
          {valueOptions.options?.map((option) => {
            const isChecked = selectedValues.includes(option.value);
            return (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`option-${option.value}`}
                  checked={isChecked}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      field.onChange([...selectedValues, option.value]);
                    } else {
                      field.onChange(
                        selectedValues.filter((v: any) => v !== option.value),
                      );
                    }
                  }}
                />
                <label
                  htmlFor={`option-${option.value}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {option.label}
                </label>
              </div>
            );
          })}
        </div>
      );
    }

    case 'select': {
      // Single select - editing existing values only. Never render a null/placeholder option.
      const options = (valueOptions.options ?? []) as {
        value: any;
        label: string;
      }[];
      const firstOptionValue = options[0] ? String(options[0].value) : '';
      const resolvedValue =
        field.value !== undefined && field.value !== null
          ? String(field.value)
          : firstOptionValue;

      return (
        <select
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          value={resolvedValue}
          onChange={(e) => {
            const value = e.target.value;
            // Convert to appropriate type based on option value type
            const option = options.find((opt) => String(opt.value) === value);
            field.onChange(option ? option.value : value);
          }}
        >
          {options.map((option) => (
            <option key={option.value} value={String(option.value)}>
              {option.label}
            </option>
          ))}
        </select>
      );
    }

    case 'boolean':
      return (
        <div className="flex items-center space-x-2">
          <Switch
            checked={Boolean(field.value)}
            onCheckedChange={(checked) => field.onChange(checked)}
          />
          <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {field.value ? 'True' : 'False'}
          </label>
        </div>
      );

    case 'number':
      return (
        <Input
          type="number"
          placeholder="Enter a number"
          value={field.value ?? ''}
          onChange={(e) => {
            const val = e.target.value;
            field.onChange(val === '' ? '' : Number(val));
          }}
        />
      );

    case 'json':
      return (
        <div>
          <Textarea
            placeholder="Enter valid JSON"
            className="h-32 font-mono text-xs"
            value={
              typeof field.value === 'string'
                ? field.value
                : JSON.stringify(field.value, null, 2)
            }
            onChange={(e) => field.onChange(e.target.value)}
          />
          {jsonError && (
            <div className="mt-1 text-sm text-red-600">{jsonError}</div>
          )}
        </div>
      );

    case 'string':
    default:
      return (
        <Input
          placeholder="Enter text"
          value={field.value ?? ''}
          onChange={(e) => field.onChange(e.target.value)}
        />
      );
  }
}

interface SettingSidebarProps {
  setting: any;
  showMenu?: boolean;
  setIsEditMode?: (isEdit: boolean) => void;
  handleToggleStatus?: () => void;
  handleDelete?: () => void;
}

function SettingSidebar({
  setting,
  showMenu = true,
  setIsEditMode,
  handleToggleStatus,
  handleDelete,
}: SettingSidebarProps) {
  return (
    <div className="flex w-1/3 flex-col justify-between overflow-scroll bg-stone-light px-6 py-10">
      <div className="flex flex-col gap-6">
        <div className="flex justify-between">
          <div className="flex flex-row items-center gap-4">
            <div>
              <div className="text-base font-medium text-dark">
                {setting.name}
              </div>
              <div className="flex flex-col text-[11px] font-medium text-stone/70">
                {setting.required && (
                  <span className="text-orange">Required</span>
                )}
              </div>
            </div>
          </div>
          {showMenu && setIsEditMode && handleToggleStatus && handleDelete && (
            <div className="flex flex-row items-start gap-4">
              <SettingMiscMenu
                onEdit={() => setIsEditMode(true)}
                onToggleStatus={handleToggleStatus}
                onDelete={handleDelete}
                isRequired={setting.required}
                currentStatus={setting.status}
              />
            </div>
          )}
        </div>

        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">
            Basic Information
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Status</div>
            <div
              className={`inline-flex w-fit rounded-full px-2 py-1 text-xs font-medium ${
                setting.status === 'active'
                  ? 'bg-green-100 text-green-800'
                  : setting.status === 'inactive'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-red-100 text-red-800'
              }`}
            >
              {setting.status}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Key</div>
            <div className="text-xs font-normal text-dark">{setting.key}</div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Name</div>
            <div className="text-xs font-normal text-dark">{setting.name}</div>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <div className="text-sm font-normal text-orange">
            Additional Details
          </div>
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Required</div>
            <div className="text-xs font-normal text-dark">
              {setting.required ? 'Yes' : 'No'}
            </div>
          </div>
          {(setting.startDate || setting.endDate) && (
            <>
              {setting.startDate && (
                <div className="flex flex-col gap-1">
                  <div className="text-xs font-medium text-stone/70">
                    Start Date
                  </div>
                  <div className="text-xs font-normal text-dark">
                    {format(new Date(setting.startDate), 'MMM dd, yyyy')}
                  </div>
                </div>
              )}
              {setting.endDate && (
                <div className="flex flex-col gap-1">
                  <div className="text-xs font-medium text-stone/70">
                    End Date
                  </div>
                  <div className="text-xs font-normal text-dark">
                    {format(new Date(setting.endDate), 'MMM dd, yyyy')}
                  </div>
                </div>
              )}
            </>
          )}
          <div className="flex flex-col gap-1">
            <div className="text-xs font-medium text-stone/70">Created At</div>
            <div className="text-xs font-normal text-dark">
              {format(new Date(setting.createdAt), 'MMM dd, yyyy')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function SettingInfo({ settingId, onClose }: SettingInfoProps) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [jsonError, setJsonError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState('details');
  const [showDateFields, setShowDateFields] = useState(false);
  const [startDatePopoverOpen, setStartDatePopoverOpen] = useState(false);
  const [endDatePopoverOpen, setEndDatePopoverOpen] = useState(false);
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);

  const { data: setting, isPending, error, refetch } = useGetSetting(settingId);

  const updateMutation = useUpdateSetting();
  const deleteMutation = useDeleteSetting();

  const form = useForm<z.infer<typeof updateSettingSchema>>({
    resolver: zodResolver(updateSettingSchema),
  });

  useEffect(() => {
    if (setting) {
      setIsEditMode(false);

      // Show date fields if endDate is set
      setShowDateFields(!!setting.endDate);

      // For JSON type, stringify the value for editing
      // Multi-select keeps arrays as-is
      let formValue = setting.value;
      if (
        setting.valueOptions?.type === 'json' &&
        typeof setting.value !== 'string'
      ) {
        formValue = JSON.stringify(setting.value, null, 2);
      }

      form.reset({
        name: setting.name,
        description: setting.description || '',
        value: formValue,
        status: setting.status,
        required: setting.required,
        startDate: setting.startDate || new Date(Date.now()).toISOString(),
        endDate: setting.endDate || '',
      });
    }
  }, [setting, form]);

  const validateJson = (value: string) => {
    if (!value.trim()) return null;
    try {
      JSON.parse(value);
      return null;
    } catch {
      return 'Invalid JSON format';
    }
  };

  const onSubmit = async (data: z.infer<typeof updateSettingSchema>) => {
    setJsonError(null);

    // Process value based on valueType
    let processedValue = data.value;

    if (setting?.valueOptions?.type === 'json') {
      // For JSON type, validate and parse the string
      const jsonValidationError = validateJson(data.value);
      if (jsonValidationError) {
        setJsonError(jsonValidationError);
        return;
      }
      try {
        processedValue = JSON.parse(data.value);
      } catch {
        setJsonError('Invalid JSON format');
        return;
      }
    }
    // For other types (string, number, boolean), the value is already processed by the form controls

    const { required: _required, ...dataWithoutRequired } = data;
    const settingData = {
      ...dataWithoutRequired,
      value: processedValue,
      description: data.description || null,
      startDate: data.startDate || new Date(Date.now()).toISOString(),
      endDate: data.endDate || null,
    };

    try {
      await updateMutation.mutateAsync({
        id: settingId,
        data: settingData,
      });
      await refetch();
      toast({
        title: 'Setting updated',
        description: 'The setting has been updated successfully.',
      });
      setIsEditMode(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to save setting',
        variant: 'destructive',
      });
    }
  };

  const handleToggleStatus = async () => {
    if (!setting || setting.required) return;

    const newStatus = setting.status === 'active' ? 'inactive' : 'active';

    try {
      await updateMutation.mutateAsync({
        id: settingId,
        data: { status: newStatus },
      });

      await refetch();
      toast({
        title: 'Status updated',
        description: `Setting status changed to ${newStatus}.`,
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update status',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async () => {
    if (!setting || setting.required) return;

    if (
      window.confirm(
        'Are you sure you want to delete this setting? This action cannot be undone.',
      )
    ) {
      try {
        await deleteMutation.mutateAsync(settingId);
        toast({
          title: 'Setting deleted',
          description: 'The setting has been deleted successfully.',
        });
        onClose();
      } catch (error: any) {
        toast({
          title: 'Error',
          description:
            error.response?.data?.message || 'Failed to delete setting',
          variant: 'destructive',
        });
      }
    }
  };

  if (isPending) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <Loader className="w-full bg-white" />
      </div>
    );
  }

  // Edit mode for existing settings
  if (isEditMode && setting) {
    return (
      <div className="relative flex h-full w-full grow bg-white">
        <SettingSidebar setting={setting} showMenu={false} />
        <div className="flex w-2/3 flex-col overflow-scroll">
          <div className="sticky top-0 z-10 bg-white px-10 pt-10">
            <div className="mb-6 flex flex-row justify-between">
              <h2 className="text-xl font-semibold text-denim">Edit Setting</h2>
              <XIcon
                size={24}
                className="cursor-pointer text-denim"
                onClick={() => setIsEditMode(false)}
              />
            </div>
          </div>

          <div className="px-10 pb-10">
            <Form {...form}>
              <form
                ref={formRef}
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-5"
              >
                <div className="grid grid-cols-1 gap-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter setting name"
                            {...field}
                            value={field.value ?? ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Description of this setting..."
                            {...field}
                            value={field.value ?? ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Value</FormLabel>
                        <FormControl>
                          {renderValueField(field, setting, jsonError)}
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <FormControl>
                            <select
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              value={field.value}
                              onChange={(e) => field.onChange(e.target.value)}
                              disabled={setting.required}
                            >
                              <option value="active">Active</option>
                              <option value="inactive">Inactive</option>
                              <option value="archived">Archived</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Date Fields Toggle */}
                  <div className="flex items-center space-x-3 space-y-0 rounded-md border p-4">
                    <Switch
                      checked={showDateFields}
                      onCheckedChange={(checked) => {
                        setShowDateFields(checked);
                        if (!checked) {
                          // Set startDate to current UTC timestamp when disabling
                          form.setValue(
                            'startDate',
                            new Date(Date.now()).toISOString(),
                          );
                          form.setValue('endDate', '');
                        } else {
                          // Set startDate to now if empty when enabling
                          const currentStartDate = form.getValues('startDate');
                          if (!currentStartDate) {
                            form.setValue(
                              'startDate',
                              new Date(Date.now()).toISOString(),
                            );
                          }
                        }
                      }}
                      disabled={setting.required}
                    />
                    <div className="space-y-1 leading-none">
                      <label className="text-sm font-medium">
                        Set Date Range
                      </label>
                      <p className="text-xs text-muted-foreground">
                        Configure when this setting should be active
                      </p>
                    </div>
                  </div>

                  {/* Date Fields - Only show when enabled */}
                  {showDateFields && (
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="startDate"
                        render={({ field }) => {
                          const startDate = field.value
                            ? new Date(field.value)
                            : new Date(Date.now());
                          return (
                            <FormItem>
                              <FormLabel>Start Date</FormLabel>
                              <FormControl>
                                <div className="space-y-2">
                                  <Popover
                                    open={startDatePopoverOpen}
                                    onOpenChange={setStartDatePopoverOpen}
                                  >
                                    <PopoverTrigger asChild>
                                      <button
                                        type="button"
                                        className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        disabled={setting.required}
                                      >
                                        {format(startDate, 'PPP')}
                                      </button>
                                    </PopoverTrigger>
                                    <PopoverContent
                                      className="w-auto p-0"
                                      align="start"
                                    >
                                      <Calendar
                                        mode="single"
                                        selected={startDate}
                                        onSelect={(date) => {
                                          if (date) {
                                            field.onChange(date.toISOString());
                                          } else {
                                            field.onChange(
                                              new Date(
                                                Date.now(),
                                              ).toISOString(),
                                            );
                                          }
                                          setStartDatePopoverOpen(false);
                                        }}
                                        initialFocus
                                      />
                                    </PopoverContent>
                                  </Popover>
                                  <button
                                    type="button"
                                    onClick={() => {
                                      field.onChange(
                                        new Date(Date.now()).toISOString(),
                                      );
                                    }}
                                    className="rounded-sm bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 hover:bg-gray-200"
                                    disabled={setting.required}
                                  >
                                    Reset to Now
                                  </button>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          );
                        }}
                      />

                      <FormField
                        control={form.control}
                        name="endDate"
                        render={({ field }) => {
                          const endDate = field.value
                            ? new Date(field.value)
                            : null;
                          return (
                            <FormItem>
                              <FormLabel>End Date</FormLabel>
                              <FormControl>
                                <div className="space-y-2">
                                  <Popover
                                    open={endDatePopoverOpen}
                                    onOpenChange={setEndDatePopoverOpen}
                                  >
                                    <PopoverTrigger asChild>
                                      <button
                                        type="button"
                                        className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        disabled={setting.required}
                                      >
                                        {endDate
                                          ? format(endDate, 'PPP')
                                          : 'Select end date'}
                                      </button>
                                    </PopoverTrigger>
                                    <PopoverContent
                                      className="w-auto p-0"
                                      align="start"
                                    >
                                      <Calendar
                                        mode="single"
                                        selected={endDate || undefined}
                                        onSelect={(date) => {
                                          if (date) {
                                            field.onChange(date.toISOString());
                                          } else {
                                            field.onChange('');
                                          }
                                          setEndDatePopoverOpen(false);
                                        }}
                                        initialFocus
                                      />
                                    </PopoverContent>
                                  </Popover>
                                  {endDate && (
                                    <button
                                      type="button"
                                      onClick={() => {
                                        field.onChange('');
                                      }}
                                      className="rounded-sm bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 hover:bg-gray-200"
                                      disabled={setting.required}
                                    >
                                      Clear
                                    </button>
                                  )}
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          );
                        }}
                      />
                    </div>
                  )}
                </div>

                <div className="flex justify-between border-t pt-6">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="tertiary"
                      onClick={() => setIsEditMode(false)}
                      disabled={updateMutation.isPending}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={updateMutation.isPending}>
                      {updateMutation.isPending
                        ? 'Saving...'
                        : 'Update Setting'}
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    );
  }

  // View mode for existing settings with split layout
  if (!isEditMode && setting) {
    return (
      <div className="relative flex h-full w-full grow bg-white">
        {/* Use the reusable sidebar component with menu */}
        <SettingSidebar
          setting={setting}
          setIsEditMode={setIsEditMode}
          handleToggleStatus={handleToggleStatus}
          handleDelete={handleDelete}
        />

        {/* Right content area with tabs */}
        <div className="flex w-2/3 flex-col overflow-scroll">
          <Tabs
            defaultValue={selectedTab}
            onValueChange={setSelectedTab}
            className="relative grid w-full grid-rows-[auto_1fr]"
          >
            <div className="sticky top-0 z-10 bg-white px-10 pt-10">
              <div className="flex flex-row justify-end text-denim">
                <XIcon size={24} className="cursor-pointer" onClick={onClose} />
              </div>
              <TabsList className="h-fit w-full justify-start gap-5 rounded-none border-b-[1px] border-denim-muted bg-white p-0 text-denim">
                <TabsTrigger
                  className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                  value="details"
                >
                  Details
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Details tab content */}
            <TabsContent className="px-10" value="details">
              <div className="mb-5 mt-10 space-y-6">
                <div className="text-2xl font-medium text-denim">
                  Setting Details
                </div>

                {/* Setting Name */}
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-denim">Name</h3>
                  <div className="rounded-lg border p-4 shadow-sm">
                    <p className="text-sm font-medium">{setting.name}</p>
                  </div>
                </div>

                {/* Setting Value */}
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-denim">Value</h3>
                  <div className="rounded-lg border p-4 shadow-sm">
                    {setting.valueOptions?.type === 'multi-select' &&
                    Array.isArray(setting.value) ? (
                      <ul className="list-inside list-disc space-y-1 text-sm">
                        {setting.value.map((selectedValue: any) => {
                          const option = setting.valueOptions?.options?.find(
                            (opt: any) => opt.value === selectedValue,
                          );
                          return (
                            <li key={selectedValue}>
                              {option ? option.label : selectedValue}
                            </li>
                          );
                        })}
                      </ul>
                    ) : (
                      <pre className="whitespace-pre-wrap break-all text-sm">
                        {typeof setting.value === 'string'
                          ? setting.value
                          : JSON.stringify(setting.value, null, 2)}
                      </pre>
                    )}
                  </div>
                </div>

                {/* Setting Description */}
                {setting.description && (
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium text-denim">
                      Description
                    </h3>
                    <div className="rounded-lg border p-4 shadow-sm">
                      <p className="text-sm">{setting.description}</p>
                    </div>
                  </div>
                )}

                {/* Setting Information */}
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-denim">
                    Information
                  </h3>
                  <div className="rounded-lg border p-4 shadow-sm">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs font-medium text-gray-500">Key</p>
                        <p className="text-sm font-medium">{setting.key}</p>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-gray-500">
                          Status
                        </p>
                        <div
                          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                            setting.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : setting.status === 'inactive'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {setting.status}
                        </div>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-gray-500">
                          Required
                        </p>
                        <p className="text-sm font-medium">
                          {setting.required ? 'Yes' : 'No'}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-gray-500">
                          Created
                        </p>
                        <p className="text-sm font-medium">
                          {format(new Date(setting.createdAt), 'MMM dd, yyyy')}
                        </p>
                      </div>
                      {(setting.startDate || setting.endDate) && (
                        <div className="col-span-2">
                          <p className="text-xs font-medium text-gray-500">
                            Date Range
                          </p>
                          <p className="text-sm font-medium">
                            {setting.startDate &&
                              `Start: ${format(new Date(setting.startDate), 'MMM dd, yyyy')}`}
                            {setting.startDate && setting.endDate && ' | '}
                            {setting.endDate &&
                              `End: ${format(new Date(setting.endDate), 'MMM dd, yyyy')}`}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }
}
