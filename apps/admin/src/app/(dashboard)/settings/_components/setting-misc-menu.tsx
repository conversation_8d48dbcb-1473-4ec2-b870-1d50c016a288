import { useMemo } from 'react';
import { EllipsisIcon } from 'lucide-react';

import { cn } from '@willow/ui';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@willow/ui/base/dropdown-menu';

interface ActionType {
  name: string;
  handleAction?: () => void;
  className?: string;
}

export const SettingMiscMenu = ({
  onEdit,
  onToggleStatus,
  onDelete,
  isRequired,
  currentStatus,
}: {
  onEdit: () => void;
  onToggleStatus: () => void;
  onDelete: () => void;
  isRequired: boolean;
  currentStatus: 'active' | 'inactive' | 'archived';
}) => {
  const actions: ActionType[] = useMemo(() => {
    const menuItems = [
      {
        name: 'Edit Setting',
        handleAction: onEdit,
      },
      { name: 'separator-1' },
      {
        name:
          currentStatus === 'active'
            ? 'Deactivate Setting'
            : 'Activate Setting',
        className: currentStatus === 'active' ? 'text-red-500' : undefined,
        handleAction: onToggleStatus,
      },
    ];

    if (!isRequired) {
      menuItems.push({ name: 'separator-2' });
      menuItems.push({
        name: 'Delete Setting',
        className: 'text-red-700 font-semibold',
        handleAction: onDelete,
      });
    }

    return menuItems;
  }, [currentStatus, onEdit, onToggleStatus, onDelete, isRequired]);

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="cursor-pointer">
            <EllipsisIcon size={24} color="#2F4C78" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-44 px-0" align="end">
          {actions.map((action) => {
            if (action.name.includes('separator'))
              return (
                <DropdownMenuSeparator
                  key={action.name}
                  className="mx-auto w-10/12"
                />
              );

            return (
              <DropdownMenuItem
                key={action.name}
                onSelect={() => {
                  if (action.handleAction) {
                    action.handleAction();
                  }
                }}
                className={cn(
                  'cursor-pointer px-4 py-3 text-xs font-normal focus:bg-[#ffecca] focus:text-[#2f4c78]',
                  action.className,
                )}
              >
                {action.name}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
