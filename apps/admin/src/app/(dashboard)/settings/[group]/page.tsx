'use client';

import React from 'react';
import { redirect } from 'next/navigation';

import { SettingsTable } from '../_components/settings-table';

const SettingGroupsList = ['all'];
type SettingGroup = 'all';

export default function Page({
  params: { group },
}: {
  params: { group: SettingGroup };
}) {
  if (!SettingGroupsList.includes(group)) return redirect('/settings/all');
  return <SettingsTable />;
}
