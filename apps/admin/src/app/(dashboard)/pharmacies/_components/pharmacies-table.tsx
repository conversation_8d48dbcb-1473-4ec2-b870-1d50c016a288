'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { LinkIcon, MapPinIcon, PackageIcon, XIcon } from 'lucide-react';
import { parseAsString, useQueryState } from 'nuqs';

import { cn } from '@willow/ui';
import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@willow/ui/base/tabs';
import { useToast } from '@willow/ui/base/use-toast';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { ColumnHeader } from '@willow/ui/column-header';
import { Loader } from '@willow/ui/loader';
import { Pagination } from '@willow/ui/pagination';
import { SearchInput } from '@willow/ui/search-input';
import {
  usePaginationSearchParams,
  useSortingQueryState,
} from '@willow/utils/table';

import type { ListedPharmacy } from '~/hooks/pharmacies';
import {
  useDeactivatePharmacy,
  useDeletePharmacy,
  useGetPharmacies,
  useGetPharmacy,
  useReactivatePharmacy,
} from '~/hooks/pharmacies';
import { useGetPharmacyPatientCountsByState } from '~/hooks/pharmacy';
import { usePharmacyTransferDrawer } from './pharmacy-hooks';
import { PharmacyInfoEdit } from './pharmacy-info';
import { PharmacyMappings } from './pharmacy-mappings';
import { PharmacyMiscMenu } from './pharmacy-misc-menu';
import { PharmacyTransferDrawer } from './pharmacy-transfer-drawer';

export function PharmaciesTable() {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useQueryState(
    'query',
    parseAsString.withDefault(''),
  );
  const [pagination, setPagination] = usePaginationSearchParams();
  const [sorting, setSorting] = useSortingQueryState([
    {
      id: 'regularPriority',
      desc: true,
    },
  ]);

  const [selectedPharmacyId, setSelectedPharmacyId] = useQueryState(
    'pharmacyId',
    {
      defaultValue: '',
    },
  );

  const [selectedTab, setSelectedTab] = useQueryState('tab', {
    defaultValue: 'states',
    parse: (value) => {
      return ['states', 'products', 'mappings'].includes(value)
        ? value
        : 'states';
    },
  });

  const [isEditMode, setIsEditMode] = useQueryState('edit', {
    defaultValue: 'false',
  });

  // Query parameter for showing inactive pharmacies
  const [showInactive, setShowInactive] = useQueryState(
    'showInactive',
    parseAsString.withDefault('false'),
  );

  const fetchParams = useMemo(
    () => ({
      sortBy: sorting.length > 0 ? sorting[0]?.id : undefined,
      direction:
        sorting.length > 0
          ? sorting[0]?.desc
            ? ('desc' as const)
            : ('asc' as const)
          : ('asc' as const),
      page: pagination.page,
      limit: pagination.pageSize,
      search: query ?? undefined,
      showInactive: showInactive === 'true',
    }),
    [query, pagination, sorting, showInactive],
  );

  const { data, isPending, isError } = useGetPharmacies(fetchParams);
  const { data: selectedPharmacy, isPending: isLoadingPharmacy } =
    useGetPharmacy(selectedPharmacyId === 'new' ? '' : selectedPharmacyId);
  const { mutateAsync: deactivatePharmacy } = useDeactivatePharmacy();
  const { mutateAsync: reactivatePharmacy } = useReactivatePharmacy();
  const { mutateAsync: deletePharmacy } = useDeletePharmacy();
  const [_isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Use custom hook for pharmacy transfer drawer
  const { drawer: transferDrawer, handleOpen: openTransferDrawer } =
    usePharmacyTransferDrawer();

  const columns: ColumnDef<ListedPharmacy>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: () => <ColumnHeader label="Name" sortKey="name" />,
        cell: ({ row }) => (
          <div className="text-sm text-denim">{row.original.name}</div>
        ),
      },
      {
        accessorKey: 'patientCount',
        header: () => (
          <ColumnHeader label="Patients" sortKey={'patientCount'} />
        ),
        cell: ({ row }) => {
          const count = row.original.patientCount ?? 0;
          return (
            <div className="text-sm text-denim">
              <span className="inline-flex items-center rounded-full bg-denim-light/20 px-2.5 py-0.5 text-sm font-medium text-denim">
                {count}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: 'enabled',
        header: () => <ColumnHeader label="Status" sortKey="enabled" />,
        cell: ({ row }) => {
          const isEnabled = row.original.enabled;
          return (
            <div className="text-sm">
              {isEnabled ? (
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  Enabled
                </span>
              ) : (
                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                  Disabled
                </span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'enableApi',
        header: () => <ColumnHeader label="API Enabled" sortKey="enableApi" />,
        cell: ({ row }) => {
          const apiStatus = row.original.enableApi;
          return (
            <div className="text-sm">
              {apiStatus === true ? (
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  Enabled
                </span>
              ) : apiStatus === false ? (
                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                  Disabled
                </span>
              ) : (
                <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                  N/A
                </span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'doseSpotPharmacyId',
        header: () => (
          <ColumnHeader label="DoseSpot ID" sortKey="doseSpotPharmacyId" />
        ),
        cell: ({ row }) => (
          <div className="text-sm text-denim">
            {row.original.doseSpotPharmacyId}
          </div>
        ),
      },
      {
        accessorKey: 'color',
        header: () => <ColumnHeader label="Color" sortKey="color" />,
        cell: ({ row }) => {
          const color = row.original.color;
          return (
            <div className="text-sm text-dark">
              {color ? (
                <div className="flex items-center">
                  <div
                    className="mr-2 h-4 w-4 rounded-full"
                    style={{ backgroundColor: color }}
                  ></div>
                </div>
              ) : (
                '-'
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'regularPriority',
        header: () => (
          <ColumnHeader label="Regular Priority" sortKey="regularPriority" />
        ),
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            {row.original.regularPriority || '0'}
          </div>
        ),
      },
      {
        accessorKey: 'usingGLP1Priority',
        header: () => (
          <ColumnHeader
            label="High Dose Priority"
            sortKey="usingGLP1Priority"
          />
        ),
        cell: ({ row }) => (
          <div className="text-sm text-dark">
            {row.original.usingGLP1Priority || '0'}
          </div>
        ),
      },
    ],
    [],
  );

  // Get the pharmacies from the API result
  const pharmacies = useMemo(() => {
    return data?.pharmacies || [];
  }, [data]);

  // Get the pharmacy's states, separated into enabled and disabled
  const { servingStates, supportedStates } = useMemo(() => {
    if (!selectedPharmacy) return { servingStates: [], supportedStates: [] };

    const serving: { name: string; id: string; code: string }[] = [];
    const supported: { name: string; id: string; code: string }[] = [];

    if (
      selectedPharmacy.PharmacyOnState &&
      Array.isArray(selectedPharmacy.PharmacyOnState)
    ) {
      selectedPharmacy.PharmacyOnState.forEach((relation) => {
        if (relation.state) {
          const stateInfo = {
            name: relation.state.name || '',
            id: relation.state.id || '',
            code: relation.state.code || '',
          };

          if (relation.state.enabled) {
            serving.push(stateInfo);
          } else {
            supported.push(stateInfo);
          }
        }
      });
    }

    // Sort states alphabetically by name
    return {
      servingStates: serving.sort((a, b) => a.name.localeCompare(b.name)),
      supportedStates: supported.sort((a, b) => a.name.localeCompare(b.name)),
    };
  }, [selectedPharmacy]);

  // Get patient counts by state for the selected pharmacy
  const { data: patientCountsData } = useGetPharmacyPatientCountsByState(
    selectedPharmacyId === 'new' ? '' : selectedPharmacyId,
  );

  // Get states with patients that are not currently associated with the pharmacy
  const disabledStatesWithPatients = useMemo(() => {
    if (!patientCountsData?.statePatientCounts) return [];

    // Filter states that are not associated with the pharmacy but have patients
    return patientCountsData.statePatientCounts
      .filter(
        (stateCount) => !stateCount.isAssociated && stateCount.patientCount > 0,
      )
      .sort((a, b) => a.stateName.localeCompare(b.stateName));
  }, [patientCountsData]);

  // Create a map of state IDs to patient counts
  const statePatientCounts = useMemo(() => {
    const counts: Record<string, number> = {};

    // Initialize all states to 0 patients
    [...servingStates, ...supportedStates].forEach((state) => {
      counts[state.id] = 0;
    });

    // If we have patient counts data, use it
    if (patientCountsData?.statePatientCounts) {
      patientCountsData.statePatientCounts.forEach((stateCount) => {
        if (stateCount.patientCount > 0) {
          counts[stateCount.stateId] = stateCount.patientCount;
        }
      });
    }

    return counts;
  }, [patientCountsData, servingStates, supportedStates]);

  const table = useReactTable({
    data: pharmacies,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
    manualPagination: true,
    pageCount: data?.pagination?.totalPages || 1,
  });

  const handleSearchSubmit = useCallback(
    (query: string | null) => {
      void setQuery(query);
      void setPagination({ page: 1 });
    },
    [setPagination, setQuery],
  );

  const handleCloseDrawer = () => {
    void setSelectedPharmacyId('');
    void setIsEditMode('false');
  };

  const handleDeactivate = async () => {
    if (
      selectedPharmacyId &&
      selectedPharmacyId !== 'new' &&
      window.confirm('Are you sure you want to deactivate this pharmacy?')
    ) {
      setIsSubmitting(true);
      try {
        await deactivatePharmacy(selectedPharmacyId);
        // The cache is already invalidated in the hook
        // No need to reload the page
        toast({
          title: 'Success',
          description: 'Pharmacy deactivated successfully',
        });
      } catch (error) {
        console.error('Error deactivating pharmacy:', error);

        // Extract the error message from the response if available
        const errorMessage = 'Failed to deactivate pharmacy';

        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleReactivate = async () => {
    if (
      selectedPharmacyId &&
      selectedPharmacyId !== 'new' &&
      window.confirm('Are you sure you want to reactivate this pharmacy?')
    ) {
      setIsSubmitting(true);
      try {
        await reactivatePharmacy(selectedPharmacyId);
        // The cache is already invalidated in the hook
        // No need to reload the page
        toast({
          title: 'Success',
          description: 'Pharmacy reactivated successfully',
        });
      } catch (error) {
        console.error('Error reactivating pharmacy:', error);
        toast({
          title: 'Error',
          description: 'Failed to reactivate pharmacy',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleDelete = async () => {
    if (
      selectedPharmacyId &&
      selectedPharmacyId !== 'new' &&
      window.confirm(
        'Are you sure you want to permanently delete this pharmacy? This action cannot be undone.',
      )
    ) {
      setIsSubmitting(true);
      try {
        await deletePharmacy(selectedPharmacyId);
        // Close the drawer since the pharmacy no longer exists
        handleCloseDrawer();
        toast({
          title: 'Success',
          description: 'Pharmacy permanently deleted',
        });
      } catch (error) {
        console.error('Error deleting pharmacy:', error);

        // Extract the error message from the response if available
        const errorMessage = 'Failed to delete pharmacy';

        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Handle opening the transfer drawer
  const handleOpenTransferDrawer = () => {
    if (selectedPharmacy) {
      openTransferDrawer(selectedPharmacy);
    }
  };

  return (
    <div className="grid h-[calc(100vh-96px)] w-full grid-rows-[auto_1fr_auto]">
      <div className="w-full">
        <div className="mb-5 text-2xl font-medium text-denim">Pharmacies</div>
        <SearchInput
          className="mb-5"
          ref={searchInputRef}
          onSearch={handleSearchSubmit}
          placeholder="Search pharmacies"
          defaultValue={query}
        />
      </div>

      <div className="relative flex-1 overflow-auto">
        <Drawer
          direction="right"
          open={selectedPharmacyId !== ''}
          onOpenChange={(value) => {
            if (!value) handleCloseDrawer();
          }}
        >
          <div className="h-full w-full">
            {isPending ? (
              <div className="flex h-40 w-full items-center justify-center">
                <Loader size="lg" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          key={header.id}
                          className="font-bold text-dark"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (
                      <DrawerTrigger
                        key={row.id}
                        asChild
                        onClick={() => {
                          void setSelectedPharmacyId(row.original.id);
                          void setIsEditMode('false');
                        }}
                      >
                        <TableRow
                          data-state={
                            selectedPharmacyId === row.original.id && 'selected'
                          }
                          className="h-[60px] cursor-pointer hover:bg-muted/50"
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id} className="p-2">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      </DrawerTrigger>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        {isError ? 'Error loading pharmacies.' : 'No results.'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </div>

          {selectedPharmacyId && (
            <DrawerPortal>
              <DrawerOverlay className="fixed inset-0 bg-dark/40" />
              <DrawerContent className="fixed bottom-0 right-0 top-0 z-10 flex w-[1000px] !touch-none !select-text">
                <DrawerTitle className="hidden">
                  Pharmacy Information
                </DrawerTitle>

                {selectedPharmacyId === 'new' ? (
                  <PharmacyInfoEdit
                    pharmacyId={selectedPharmacyId}
                    handleClose={handleCloseDrawer}
                  />
                ) : (
                  <div className="relative flex h-full w-full grow bg-white">
                    {isLoadingPharmacy ? (
                      <Loader className="w-full bg-white" />
                    ) : (
                      <>
                        <div className="flex w-[270px] flex-col justify-between overflow-scroll bg-stone-light px-6 py-10">
                          <div className="flex flex-col gap-6">
                            <div className="flex justify-between">
                              <div className="flex flex-row items-center gap-4">
                                <div className="relative flex h-[50px] w-[50px] items-center justify-center rounded-full bg-denim-light text-xl text-white">
                                  {selectedPharmacy?.name?.charAt(0) || 'P'}
                                </div>
                                <div>
                                  <div className="text-base font-medium text-dark">
                                    {selectedPharmacy?.name}
                                  </div>
                                  <div className="text-[11px] font-medium text-stone/70">
                                    Pharmacy
                                  </div>
                                </div>
                              </div>
                              {isEditMode !== 'true' && (
                                <PharmacyMiscMenu
                                  isActive={selectedPharmacy?.enabled ?? false}
                                  setIsEditMode={setIsEditMode}
                                  handleDeactivate={handleDeactivate}
                                  handleReactivate={handleReactivate}
                                  handleDelete={handleDelete}
                                  handleOpenTransferDrawer={
                                    handleOpenTransferDrawer
                                  }
                                  hasPatients={
                                    selectedPharmacy?.patientCount
                                      ? selectedPharmacy.patientCount > 0
                                      : false
                                  }
                                />
                              )}
                            </div>
                            <div className="flex flex-col gap-4">
                              <div className="text-sm font-normal text-orange">
                                Basic Information
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Status
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedPharmacy?.enabled ? (
                                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                      Enabled
                                    </span>
                                  ) : (
                                    <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                      Disabled
                                    </span>
                                  )}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  API Integration
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedPharmacy?.enableApi === true ? (
                                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                      Enabled
                                    </span>
                                  ) : selectedPharmacy?.enableApi === false ? (
                                    <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                      Disabled
                                    </span>
                                  ) : (
                                    <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                                      Not Applicable
                                    </span>
                                  )}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  DoseSpot Pharmacy ID
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedPharmacy?.doseSpotPharmacyId || '-'}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Patients
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  <a
                                    href={`/patients/all?pharmacyId=${selectedPharmacy?.id}`}
                                    className="inline-flex cursor-pointer items-center rounded-full bg-denim-light/20 px-2.5 py-0.5 text-sm font-medium text-denim hover:bg-denim-light/30"
                                  >
                                    {selectedPharmacy?.patientCount}
                                  </a>
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Regular Priority
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedPharmacy?.regularPriority || '0'}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  High Dose Priority
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedPharmacy?.usingGLP1Priority || '0'}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Color
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedPharmacy?.color ? (
                                    <div className="flex items-center">
                                      <div
                                        className="mr-2 h-4 w-4 rounded-full"
                                        style={{
                                          backgroundColor:
                                            selectedPharmacy.color,
                                        }}
                                      ></div>
                                      {selectedPharmacy.color}
                                    </div>
                                  ) : (
                                    '-'
                                  )}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Slug
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedPharmacy?.slug || '-'}
                                </div>
                              </div>
                              <div className="flex flex-col gap-1">
                                <div className="text-xs font-medium text-stone/70">
                                  Created At
                                </div>
                                <div className="text-xs font-normal text-dark">
                                  {selectedPharmacy?.createdAt
                                    ? new Date(
                                        selectedPharmacy.createdAt,
                                      ).toLocaleString()
                                    : '-'}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-1 flex-col overflow-scroll">
                          {isEditMode === 'true' ? (
                            <div className="relative h-full overflow-auto">
                              <div className="sticky top-0 z-10 flex justify-end bg-white px-10 pt-10">
                                <XIcon
                                  size={24}
                                  className="cursor-pointer"
                                  onClick={() => setIsEditMode('false')}
                                />
                              </div>
                              <PharmacyInfoEdit
                                pharmacyId={selectedPharmacyId}
                                handleClose={() => setIsEditMode('false')}
                              />
                            </div>
                          ) : (
                            <Tabs
                              defaultValue={selectedTab}
                              onValueChange={setSelectedTab}
                              className="relative grid w-full grid-rows-[auto_1fr]"
                            >
                              <div className="sticky top-0 z-10 bg-white px-10 pt-10">
                                <div className="flex flex-row justify-end text-denim">
                                  <XIcon
                                    size={24}
                                    className="cursor-pointer"
                                    onClick={handleCloseDrawer}
                                  />
                                </div>
                                <TabsList className="h-fit w-full justify-start gap-5 rounded-none border-b-[1px] border-denim-muted bg-white p-0 text-denim">
                                  <TabsTrigger
                                    className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                                    value="states"
                                  >
                                    <MapPinIcon size={16} className="mr-1" />
                                    <div>Serving States</div>
                                  </TabsTrigger>
                                  <TabsTrigger
                                    className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                                    value="products"
                                  >
                                    <PackageIcon size={16} className="mr-1" />
                                    <div>Products</div>
                                  </TabsTrigger>
                                  <TabsTrigger
                                    className="rounded-none text-xs font-normal text-muted-foreground data-[state=active]:border-b-[3px] data-[state=active]:border-denim data-[state=active]:text-denim data-[state=active]:shadow-none"
                                    value="mappings"
                                  >
                                    <LinkIcon size={16} className="mr-1" />
                                    <div>Mappings</div>
                                  </TabsTrigger>
                                </TabsList>
                              </div>
                              <TabsContent className="px-10" value="states">
                                {/* Serving States (enabled) */}
                                <div className="mb-8 mt-10">
                                  <div className="mb-5 text-2xl font-medium text-denim">
                                    Serving States
                                  </div>
                                  <div className="flex flex-wrap gap-2">
                                    {servingStates.length > 0 ? (
                                      servingStates.map((state) => {
                                        const patientCount =
                                          statePatientCounts[state.id] || 0;
                                        const hasPatients = patientCount > 0;

                                        return hasPatients ? (
                                          <a
                                            key={state.id}
                                            href={`/patients/all?pharmacyId=${selectedPharmacy?.id}&stateCode=${state.code}`}
                                            className="inline-flex cursor-pointer items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800 hover:bg-green-200"
                                          >
                                            {state.name} ({patientCount})
                                          </a>
                                        ) : (
                                          <div
                                            key={state.id}
                                            className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800"
                                          >
                                            {state.name}
                                          </div>
                                        );
                                      })
                                    ) : (
                                      <div className="text-sm text-gray-500">
                                        No serving states found
                                      </div>
                                    )}
                                  </div>
                                </div>

                                {/* Disabled States with Patients */}
                                {disabledStatesWithPatients.length > 0 && (
                                  <div className="mb-8 text-stone">
                                    <div className="text-2xl font-medium">
                                      Disabled States with Patients
                                    </div>
                                    <div className="mb-5 text-sm">
                                      These states are not currently associated
                                      with this pharmacy, but there are patients
                                      assigned to them.
                                    </div>
                                    <div className="flex flex-wrap gap-2">
                                      {disabledStatesWithPatients.map(
                                        (state) => (
                                          <a
                                            key={state.stateId}
                                            href={`/patients/all?pharmacyId=${selectedPharmacy?.id}&stateCode=${state.stateCode}`}
                                            className="inline-flex cursor-pointer items-center rounded-full bg-red-100 px-3 py-1 text-sm font-medium text-red-800 hover:bg-red-200"
                                          >
                                            {state.stateName} (
                                            {state.patientCount})
                                          </a>
                                        ),
                                      )}
                                    </div>
                                  </div>
                                )}

                                {/* Supported States (disabled) */}
                                {supportedStates.length > 0 && (
                                  <div className="mb-8 text-stone">
                                    <div className="text-2xl font-medium">
                                      Disabled States
                                    </div>
                                    <div className="mb-5 text-sm">
                                      These states are supported by this
                                      pharmacy, but are not currently enabled.
                                    </div>
                                    <div className="flex flex-wrap gap-2">
                                      {supportedStates.map((state) => (
                                        <div
                                          key={state.id}
                                          className="inline-flex items-center rounded-full bg-gray-100 px-3 py-1 text-sm font-medium text-gray-800"
                                        >
                                          {state.name}
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </TabsContent>
                              <TabsContent className="px-10" value="products">
                                <div className="mb-5 mt-10 text-2xl font-medium text-denim">
                                  Products
                                </div>
                                <div className="mb-5 mt-3 text-sm text-gray-500">
                                  Products are associated with this pharmacy
                                  from the Products management section. Each
                                  product belongs to a specific pharmacy.
                                </div>

                                {/* All Products List */}
                                <div>
                                  {selectedPharmacy?.Product &&
                                  selectedPharmacy.Product.length > 0 ? (
                                    <ul className="space-y-3">
                                      {selectedPharmacy.Product.map(
                                        (product) => (
                                          <li
                                            key={product.id}
                                            className="border-b pb-3"
                                          >
                                            <a
                                              href={`/products/all?productId=${product.id}`}
                                              className="flex items-center justify-between rounded-md p-2 hover:bg-gray-50"
                                            >
                                              <div className="flex items-center">
                                                <span className="text-sm font-medium">
                                                  {product.name}
                                                </span>
                                                {!product.active && (
                                                  <span className="ml-2 inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                                    Inactive
                                                  </span>
                                                )}
                                                {product.isCore && (
                                                  <span className="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                                                    Core
                                                  </span>
                                                )}
                                              </div>

                                              <div className="flex items-center">
                                                {product.image && (
                                                  <div className="mr-2 h-[30px] w-[30px] overflow-hidden rounded-full">
                                                    <img
                                                      src={product.image}
                                                      alt={product.name}
                                                      className="h-full w-full object-cover"
                                                    />
                                                  </div>
                                                )}
                                                <svg
                                                  xmlns="http://www.w3.org/2000/svg"
                                                  viewBox="0 0 20 20"
                                                  fill="currentColor"
                                                  className="h-5 w-5 text-denim"
                                                >
                                                  <path
                                                    fillRule="evenodd"
                                                    d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                                    clipRule="evenodd"
                                                  />
                                                </svg>
                                              </div>
                                            </a>
                                          </li>
                                        ),
                                      )}
                                    </ul>
                                  ) : (
                                    <p className="text-sm text-gray-500">
                                      No products associated with this pharmacy
                                    </p>
                                  )}
                                </div>
                              </TabsContent>
                              <TabsContent className="px-0" value="mappings">
                                {selectedPharmacy?.id && (
                                  <PharmacyMappings
                                    pharmacyId={selectedPharmacy.id}
                                  />
                                )}
                              </TabsContent>
                            </Tabs>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                )}
              </DrawerContent>
            </DrawerPortal>
          )}
        </Drawer>
      </div>

      <div className={cn('flex items-center justify-between py-4')}>
        <div className="flex gap-3">
          <Button
            onClick={() => {
              void setSelectedPharmacyId('new');
              void setIsEditMode('true');
            }}
          >
            Add New Pharmacy
          </Button>
          <Button
            variant="electric"
            onClick={() => {
              void setShowInactive(showInactive === 'true' ? 'false' : 'true');
              void setPagination({ page: 1 }); // Reset to first page when toggling
            }}
          >
            {showInactive === 'true'
              ? 'Hide Disabled Pharmacies'
              : 'Show Disabled Pharmacies'}
          </Button>
        </div>
        {table.getRowModel().rows.length > 0 && (
          <Pagination
            currentPage={pagination.page}
            totalPages={table.getPageCount()}
            onPageChange={(page) => setPagination({ page })}
          />
        )}
      </div>

      {/* Pharmacy Transfer Drawer */}
      {transferDrawer.sourcePharmacy && (
        <PharmacyTransferDrawer
          isOpen={transferDrawer.isOpen}
          onClose={() => transferDrawer.handleClose()}
          sourcePharmacy={transferDrawer.sourcePharmacy}
        />
      )}
    </div>
  );
}
