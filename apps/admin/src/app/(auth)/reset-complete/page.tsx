'use client';

import Image from 'next/image';
import Link from 'next/link';
import { ArrowRightCircle } from 'lucide-react';

import { Button } from '@willow/ui/base/button';

import banner from '~/assets/png/login-banner.png';
import DotIcon from '~/assets/svg/dot.svg';

export default function ResetCompletePage() {
  return (
    <div className="flex min-h-screen w-full">
      <div className="flex flex-1 flex-col justify-between bg-denim bg-[url('../assets/png/login-flow.png')] bg-cover md:p-12 xl:p-24">
        <div>
          <div className="mb-12 flex flex-row items-center space-x-2">
            <DotIcon />
            <h3 className="text-2xl text-white underline">
              Password reset complete
            </h3>
          </div>
          <div className="px-7">
            <h1 className="text-5xl font-medium text-white">All done!</h1>
            <h1 className="text-5xl font-medium text-electric">Welcome back</h1>
            <p className="mt-4 text-xl text-white">
              Your password has been successfully reset. You can now log in with
              your new password.
            </p>
          </div>
        </div>
      </div>
      <div className="flex-1 bg-white">
        <div className="h-[386px]">
          <Image
            className="h-full w-full"
            alt="Health care provider"
            src={banner}
            style={{ objectFit: 'cover' }}
          />
        </div>
        <div className="flex justify-center px-10 py-10">
          <div className="w-full max-w-2xl space-y-12">
            <div className="space-y-4">
              <h2 className="text-3xl font-medium text-denim">
                Success! Your password has been reset.
              </h2>
              <p className="text-lg text-slate-600">
                You can now use your new password to log into your Willow admin
                account.
              </p>
            </div>

            <Link href="/login">
              <Button size={'lg'} variant="denim" className="px-12">
                GO TO LOGIN
                <ArrowRightCircle />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
