'use client';

import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAtom } from 'jotai';

import { accessTokenAtom } from '~/store/accessToken';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [accessToken] = useAtom(accessTokenAtom);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Check if the current path is related to password reset
    const isPasswordResetPath =
      pathname.includes('/reset-password') ||
      pathname.includes('/forgot-password') ||
      pathname.includes('/reset-complete');

    // Only redirect to dashboard if user is logged in and not trying to reset password
    if (accessToken && !isPasswordResetPath) {
      return router.push('/');
    }
  }, [accessToken, router, pathname]);

  return <>{children}</>;
}
