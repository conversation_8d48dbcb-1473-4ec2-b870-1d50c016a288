'use client';

import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowRightCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';

import banner from '~/assets/png/login-banner.png';
import DotIcon from '~/assets/svg/dot.svg';
import { useResetPassword } from '~/hooks/login';

const schema = z
  .object({
    token: z.string(),
    password: z
      .string()
      .min(10, 'Password must be at least 10 characters long')
      .regex(/[a-z]/, 'Password must contain at least 1 lowercase letter')
      .regex(/[A-Z]/, 'Password must contain at least 1 uppercase letter')
      .regex(/[0-9]/, 'Password must contain at least 1 number')
      .regex(/[^A-Za-z0-9]/, 'Password must contain at least 1 symbol'),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    path: ['confirmPassword'],
    message: 'Passwords does not match',
  });

export default function ResetPasswordPage({
  params,
}: {
  params: Promise<{ code: string }>;
}) {
  const { code } = React.use(params);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    values: {
      token: code,
      password: '',
      confirmPassword: '',
    },
    shouldFocusError: true,
  });

  const { mutateAsync, isPending } = useResetPassword();

  const router = useRouter();

  const onSubmit = async (data: z.infer<typeof schema>) => {
    try {
      await mutateAsync({
        token: data.token,
        password: data.password,
      });
      router.push('/reset-complete');
    } catch (e: any) {
      form.setError('confirmPassword', {
        type: 'manual',
        message: e.response.data?.message,
      });
    }
  };

  return (
    <div className="flex min-h-screen w-full">
      <div className="flex flex-1 flex-col justify-between bg-denim bg-[url('../assets/png/login-flow.png')] bg-cover md:p-12 xl:p-24">
        <div>
          <div className="mb-12 flex flex-row items-center space-x-2">
            <DotIcon />
            <h3 className="text-2xl text-white underline">
              Reset your password
            </h3>
          </div>
          <div className="px-7">
            <h1 className="text-5xl font-medium text-white">Create a new</h1>
            <h1 className="text-5xl font-medium text-electric">password</h1>
            <p className="mt-4 text-xl text-white">
              Enter your new password below to regain access to your account.
            </p>
          </div>
        </div>
      </div>
      <div className="flex-1 bg-white">
        <div className="h-[386px]">
          <Image
            className="h-full w-full"
            alt="Health care provider"
            src={banner}
            style={{ objectFit: 'cover' }}
          />
        </div>
        <div className="flex justify-center px-10 py-10">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="w-full max-w-2xl space-y-12"
            >
              <FormField
                control={form.control}
                name={'token'}
                render={({ field }) => <Input type={'hidden'} {...field} />}
              />
              <div className="space-y-10">
                <FormField
                  control={form.control}
                  name={'password'}
                  render={({ field }) => (
                    <FormItem>
                      <div className="relative w-full">
                        <FormControl>
                          <Input
                            variant="denim"
                            type="password"
                            placeholder="New Password"
                            {...field}
                          />
                        </FormControl>
                        <ArrowRightCircle className="absolute right-3 top-3 text-denim" />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={'confirmPassword'}
                  render={({ field }) => (
                    <FormItem>
                      <div className="relative w-full">
                        <FormControl>
                          <Input
                            variant="denim"
                            type="password"
                            placeholder="Confirm Password"
                            {...field}
                          />
                        </FormControl>
                        <ArrowRightCircle className="absolute right-3 top-3 text-denim" />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                size={'lg'}
                variant="denim"
                loading={isPending}
                type="submit"
                className="px-12"
              >
                RESET PASSWORD
                <ArrowRightCircle />
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
