'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowRightCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import { useToast } from '@willow/ui/base/use-toast';

import banner from '~/assets/png/login-banner.png';
import DotIcon from '~/assets/svg/dot.svg';
import { useForgotPassword } from '~/hooks/login';

const schema = z.object({
  email: z.string().trim().email({ message: 'Invalid email address' }),
});

type FormType = z.infer<typeof schema>;

export default function ForgotPasswordPage() {
  const { mutateAsync: forgotPassword, isPending } = useForgotPassword();
  const router = useRouter();
  const { toast } = useToast();

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async ({ email }: FormType) => {
    try {
      await forgotPassword({ email });
    } catch {
      // Ignore errors - for security reasons we always show success
      // to prevent email enumeration attacks
    }

    // Always show success and redirect regardless of API response
    toast({
      title: 'Password reset email sent successfully',
      description:
        'If the email you provided is associated with a Willow admin account, you will receive an email with instructions to reset your password.',
    });
    router.push('/login');
  };

  return (
    <div className="flex min-h-screen w-full">
      <div className="flex flex-1 flex-col justify-between bg-denim bg-[url('../assets/png/login-flow.png')] bg-cover md:p-12 xl:p-24">
        <div>
          <div className="mb-12 flex flex-row items-center space-x-2">
            <DotIcon />
            <h3 className="text-2xl text-white underline">
              Reset your password
            </h3>
          </div>
          <div className="px-7">
            <h1 className="text-5xl font-medium text-white">Forgot your</h1>
            <h1 className="text-5xl font-medium text-electric">password?</h1>
            <p className="mt-4 text-xl text-white">
              Enter your email address and we'll send you a link to reset your
              password.
            </p>
          </div>
        </div>
      </div>
      <div className="flex-1 bg-white">
        <div className="h-[386px]">
          <Image
            className="h-full w-full"
            alt="Health care provider"
            src={banner}
            style={{ objectFit: 'cover' }}
          />
        </div>
        <div className="flex justify-center px-10 py-10">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="w-full max-w-2xl space-y-12"
            >
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <div className="relative w-full">
                      <FormControl>
                        <Input variant="denim" placeholder="Email" {...field} />
                      </FormControl>
                      <ArrowRightCircle className="absolute right-3 top-3 text-denim" />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                size={'lg'}
                variant="denim"
                type="submit"
                className="px-12"
                loading={isPending}
              >
                RESET PASSWORD
                <ArrowRightCircle />
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
