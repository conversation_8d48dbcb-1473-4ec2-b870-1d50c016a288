-- CreateEnum
CREATE TYPE "SettingStatus" AS ENUM ('active', 'inactive', 'archived');

-- CreateTable
CREATE TABLE "Setting"
(
    "id"           TEXT            NOT NULL,
    "key"          TEXT            NOT NULL,
    "name"         TEXT            NOT NULL,
    "description"  TEXT,
    "value"        JSONB           NOT NULL,
    "valueOptions" JSONB,
    "status"       "SettingStatus" NOT NULL DEFAULT 'active',
    "required"     BOOLEAN         NOT NULL DEFAULT false,
    "startDate"    TIMESTAMP(3)             DEFAULT CURRENT_TIMESTAMP,
    "endDate"      TIMESTAMP(3),
    "createdAt"    TIMESTAMP(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt"    TIMESTAMP(3)    NOT NULL,

    CONSTRAINT "Setting_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Setting_key_key" ON "Setting" ("key");

-- CreateIndex
CREATE INDEX "Setting_key_status_idx" ON "Setting" ("key", "status");

-- CreateIndex
CREATE INDEX "Setting_status_startDate_endDate_idx" ON "Setting" ("status", "startDate", "endDate");

-- Insert initial onboarding and follow up versions setting
INSERT INTO "Setting" ("id", "key", "name", "description", "value", "valueOptions", "required", "status", "updatedAt")
VALUES (gen_random_uuid(),
        'onboardingVersion',
        'Onboarding Version',
        'Onboarding Version for new Patients',
        '"v4"'::jsonb,
        '{
          "type": "select",
          "options": [
            {
              "label": "v4",
              "value": "v4"
            },
            {
              "label": "v3",
              "value": "v3"
            },
            {
              "label": "v2",
              "value": "v2"
            }
          ]
        }'::jsonb,
        true,
        'active',
        CURRENT_TIMESTAMP);

INSERT INTO "Setting" ("id", "key", "name", "description", "value", "valueOptions", "required", "status", "updatedAt")
VALUES (gen_random_uuid(),
        'followUpVersion',
        'Follow Up Version',
        'Follow Up Version for Patients',
        '"v1"'::jsonb,
        '{
          "type": "select",
          "options": [
            {"label": "v1", "value": "v1"}
          ]
        }'::jsonb,
        true,
        'active',
        CURRENT_TIMESTAMP);